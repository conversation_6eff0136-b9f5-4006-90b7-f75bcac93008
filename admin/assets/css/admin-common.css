/**
 * 管理后台公共样式
 * 统一的样式规范和组件样式
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-28
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主色调 */
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #66b3ff;
    
    /* 辅助色 */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    
    /* 中性色 */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #343a40;
    --black: #000000;
    
    /* 边框和阴影 */
    --border-color: #dee2e6;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    
    /* 过渡动画 */
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

/* ===== 全局样式重置 ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--dark-gray);
    background-color: var(--light-gray);
}

/* ===== 布局样式 ===== */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    transition: var(--transition);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar.collapsed {
    width: 60px;
}

.main-content {
    flex: 1;
    margin-left: 250px;
    transition: var(--transition);
    background-color: var(--light-gray);
}

.main-content.expanded {
    margin-left: 60px;
}

/* ===== 侧边栏样式 ===== */
.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 600;
}

.sidebar-nav {
    padding: var(--spacing-md) 0;
}

.sidebar-nav .nav-item {
    margin-bottom: var(--spacing-xs);
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
    border-radius: 0;
}

.sidebar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.sidebar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--white);
    font-weight: 500;
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-right: var(--spacing-md);
    text-align: center;
}

.sidebar.collapsed .nav-link span {
    display: none;
}

/* ===== 顶部导航栏样式 ===== */
.top-navbar {
    background-color: var(--white);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* ===== 内容区域样式 ===== */
.content-wrapper {
    padding: var(--spacing-lg);
}

.page-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0;
    color: var(--dark-gray);
}

.page-subtitle {
    color: var(--gray);
    margin: var(--spacing-xs) 0 0 0;
}

/* ===== 卡片样式 ===== */
.card {
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: var(--spacing-lg);
}

.card-header {
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background-color: var(--light-gray);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* ===== 按钮样式增强 ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark-gray);
}

.btn-info {
    background-color: var(--info-color);
    color: var(--white);
}

/* ===== 表格样式增强 ===== */
.table {
    background-color: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--light-gray);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--dark-gray);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.table-actions {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.table-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* ===== 表单样式增强 ===== */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-xs);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

/* ===== 状态标签样式 ===== */
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 50px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status-banned {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--gray);
}

/* ===== 统计卡片样式 ===== */
.stats-card {
    background: linear-gradient(135deg, var(--white), var(--light-gray));
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stats-card .card-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stats-label {
    color: var(--gray);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.3;
}

/* ===== 通知样式 ===== */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== 加载动画 ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 250px;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .content-wrapper {
        padding: var(--spacing-md);
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* ===== 工具类 ===== */
.text-muted {
    color: var(--gray) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.bg-light {
    background-color: var(--light-gray) !important;
}

.border-light {
    border-color: var(--border-color) !important;
}

.rounded {
    border-radius: var(--border-radius) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.shadow-lg {
    box-shadow: var(--box-shadow-lg) !important;
}
