/**
 * 管理后台公共JavaScript功能
 * 提供统一的前端交互功能和工具函数
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-28
 */

class AdminCommon {
    constructor() {
        this.init();
    }

    /**
     * 初始化公共功能
     */
    init() {
        this.initTooltips();
        this.initConfirmDialogs();
        this.initAjaxSetup();
        this.initFormValidation();
        this.initDataTables();
        this.initNotifications();
        this.bindEvents();
    }

    /**
     * 初始化工具提示
     */
    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    /**
     * 初始化确认对话框
     */
    initConfirmDialogs() {
        document.addEventListener('click', (e) => {
            if (e.target.hasAttribute('data-confirm')) {
                e.preventDefault();
                const message = e.target.getAttribute('data-confirm');
                if (confirm(message)) {
                    if (e.target.tagName === 'A') {
                        window.location.href = e.target.href;
                    } else if (e.target.tagName === 'BUTTON' && e.target.form) {
                        e.target.form.submit();
                    }
                }
            }
        });
    }

    /**
     * 初始化AJAX设置
     */
    initAjaxSetup() {
        // 设置CSRF令牌
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                }
            });
        }

        // 全局AJAX错误处理
        $(document).ajaxError((event, xhr, settings, thrownError) => {
            console.error('AJAX Error:', thrownError);
            this.showNotification('请求失败，请稍后重试', 'error');
        });
    }

    /**
     * 初始化表单验证
     */
    initFormValidation() {
        // Bootstrap表单验证
        const forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach((form) => {
            form.addEventListener('submit', (event) => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }

    /**
     * 初始化数据表格
     */
    initDataTables() {
        if (typeof $.fn.DataTable !== 'undefined') {
            $('.data-table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: 'no-sort' }
                ]
            });
        }
    }

    /**
     * 初始化通知系统
     */
    initNotifications() {
        // 创建通知容器
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 侧边栏切换
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sidebar-toggle')) {
                this.toggleSidebar();
            }
        });

        // 全选/取消全选
        document.addEventListener('change', (e) => {
            if (e.target.matches('.select-all')) {
                this.toggleSelectAll(e.target);
            }
        });

        // 批量操作
        document.addEventListener('click', (e) => {
            if (e.target.matches('.batch-action')) {
                this.handleBatchAction(e.target);
            }
        });
    }

    /**
     * 显示通知
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (success, error, warning, info)
     * @param {number} duration 显示时长（毫秒）
     */
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');
        
        const typeClasses = {
            success: 'alert-success',
            error: 'alert-danger',
            warning: 'alert-warning',
            info: 'alert-info'
        };

        notification.className = `alert ${typeClasses[type]} alert-dismissible fade show`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(notification);

        // 自动隐藏
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    /**
     * 切换侧边栏
     */
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
    }

    /**
     * 全选/取消全选
     * @param {Element} checkbox 全选复选框
     */
    toggleSelectAll(checkbox) {
        const targetSelector = checkbox.getAttribute('data-target') || '.item-checkbox';
        const checkboxes = document.querySelectorAll(targetSelector);
        
        checkboxes.forEach(cb => {
            cb.checked = checkbox.checked;
        });

        this.updateBatchActionButtons();
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchActionButtons() {
        const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
        const batchButtons = document.querySelectorAll('.batch-action');
        
        batchButtons.forEach(button => {
            button.disabled = checkedBoxes.length === 0;
        });
    }

    /**
     * 处理批量操作
     * @param {Element} button 批量操作按钮
     */
    handleBatchAction(button) {
        const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
        
        if (checkedBoxes.length === 0) {
            this.showNotification('请先选择要操作的项目', 'warning');
            return;
        }

        const action = button.getAttribute('data-action');
        const confirmMessage = button.getAttribute('data-confirm');
        
        if (confirmMessage && !confirm(confirmMessage)) {
            return;
        }

        const ids = Array.from(checkedBoxes).map(cb => cb.value);
        this.performBatchAction(action, ids);
    }

    /**
     * 执行批量操作
     * @param {string} action 操作类型
     * @param {Array} ids ID数组
     */
    performBatchAction(action, ids) {
        const url = window.location.pathname;
        
        $.ajax({
            url: url,
            method: 'POST',
            data: {
                batch_action: action,
                ids: ids
            },
            success: (response) => {
                if (response.success) {
                    this.showNotification(response.message || '操作成功', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    this.showNotification(response.message || '操作失败', 'error');
                }
            },
            error: () => {
                this.showNotification('操作失败，请稍后重试', 'error');
            }
        });
    }

    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 格式化日期时间
     * @param {string|Date} date 日期
     * @returns {string} 格式化后的日期时间
     */
    formatDateTime(date) {
        const d = new Date(date);
        return d.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 复制文本到剪贴板
     * @param {string} text 要复制的文本
     */
    copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showNotification('已复制到剪贴板', 'success');
            });
        } else {
            // 兼容旧浏览器
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('已复制到剪贴板', 'success');
        }
    }

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 时间限制
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// 初始化公共功能
document.addEventListener('DOMContentLoaded', () => {
    window.adminCommon = new AdminCommon();
});

// 全局工具函数
window.showNotification = (message, type, duration) => {
    if (window.adminCommon) {
        window.adminCommon.showNotification(message, type, duration);
    }
};

window.copyToClipboard = (text) => {
    if (window.adminCommon) {
        window.adminCommon.copyToClipboard(text);
    }
};
