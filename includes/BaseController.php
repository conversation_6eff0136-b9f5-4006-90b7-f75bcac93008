<?php
/**
 * API基础控制器类
 * 提供通用的API功能，包括请求处理、响应格式化、认证验证等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @package Core
 * @since 2024-01-01
 */

require_once __DIR__ . '/../api/config/config.php';
require_once __DIR__ . '/../api/config/database.php';

abstract class BaseController {
    
    /**
     * 数据库连接实例
     * @var PDO
     */
    protected $conn;
    
    /**
     * 请求输入数据
     * @var array
     */
    protected $input;
    
    /**
     * 当前用户信息
     * @var array|null
     */
    protected $currentUser = null;
    
    /**
     * 构造函数
     * 初始化控制器基础功能
     */
    public function __construct() {
        $this->initDatabase();
        $this->setCorsHeaders();
        $this->parseInput();
    }
    
    /**
     * 初始化数据库连接
     * 
     * @return void
     * @throws Exception 当数据库连接失败时
     */
    private function initDatabase() {
        try {
            $db = new Database();
            $this->conn = $db->getConnection();
        } catch (Exception $e) {
            $this->errorResponse('数据库连接失败', API_SERVER_ERROR_CODE);
        }
    }
    
    /**
     * 设置CORS响应头
     * 
     * @return void
     */
    private function setCorsHeaders() {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        header('Access-Control-Max-Age: 86400');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
    
    /**
     * 解析请求输入数据
     * 支持JSON和表单数据
     * 
     * @return void
     */
    private function parseInput() {
        $this->input = [];
        
        // 尝试解析JSON数据
        $jsonInput = json_decode(file_get_contents('php://input'), true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($jsonInput)) {
            $this->input = $jsonInput;
        } else {
            // 使用POST数据
            $this->input = $_POST;
        }
        
        // 合并GET参数
        $this->input = array_merge($this->input, $_GET);
    }
    
    /**
     * 验证请求方法
     * 
     * @param string $expectedMethod 期望的请求方法
     * @return void
     */
    protected function validateRequestMethod($expectedMethod) {
        if ($_SERVER['REQUEST_METHOD'] !== strtoupper($expectedMethod)) {
            $this->errorResponse("只允许{$expectedMethod}请求", 405);
        }
    }
    
    /**
     * 验证必填字段
     * 
     * @param array $requiredFields 必填字段列表
     * @return void
     */
    protected function validateRequiredFields($requiredFields) {
        $missingFields = [];
        
        foreach ($requiredFields as $field) {
            if (empty($this->input[$field])) {
                $missingFields[] = $field;
            }
        }
        
        if (!empty($missingFields)) {
            $this->errorResponse('缺少必填字段: ' . implode(', ', $missingFields), API_ERROR_CODE);
        }
    }
    
    /**
     * 获取输入值
     * 
     * @param string $key 键名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getInput($key, $default = null) {
        return isset($this->input[$key]) ? trim($this->input[$key]) : $default;
    }
    
    /**
     * 验证用户认证
     * 
     * @param bool $required 是否必须认证
     * @return array|null 用户信息或null
     */
    protected function validateAuth($required = true) {
        $token = $this->getAuthToken();
        
        if (empty($token)) {
            if ($required) {
                $this->errorResponse('缺少认证令牌', API_UNAUTHORIZED_CODE);
            }
            return null;
        }
        
        // 验证JWT令牌
        require_once __DIR__ . '/jwt_helper.php';
        $jwtHelper = new JWTHelper();
        $result = $jwtHelper->validateToken($token);
        
        if (!$result) {
            if ($required) {
                $this->errorResponse('认证令牌无效', API_UNAUTHORIZED_CODE);
            }
            return null;
        }
        
        $this->currentUser = $result;
        return $result;
    }
    
    /**
     * 获取认证令牌
     * 
     * @return string|null
     */
    private function getAuthToken() {
        // 从Authorization头获取
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $matches = [];
            if (preg_match('/Bearer\s+(.+)/', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        
        // 从GET参数获取
        if (isset($_GET['token'])) {
            return $_GET['token'];
        }
        
        // 从POST数据获取
        return $this->getInput('token');
    }
    
    /**
     * 记录操作日志
     * 
     * @param string $action 操作类型
     * @param string $description 操作描述
     * @param array $context 上下文信息
     * @return void
     */
    protected function logOperation($action, $description, $context = []) {
        $logData = [
            'user_id' => $this->currentUser['id'] ?? null,
            'action' => $action,
            'description' => $description,
            'ip' => getClientIP(),
            'user_agent' => getUserAgent(),
            'context' => $context
        ];
        
        logMessage('INFO', $description, $logData);
    }
    
    /**
     * 成功响应
     * 
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @return void
     */
    protected function successResponse($data = null, $message = '操作成功') {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code(API_SUCCESS_CODE);
        
        $response = [
            'code' => API_SUCCESS_CODE,
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * 错误响应
     * 
     * @param string $message 错误消息
     * @param int $code HTTP状态码
     * @param mixed $data 附加数据
     * @return void
     */
    protected function errorResponse($message = '操作失败', $code = API_ERROR_CODE, $data = null) {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code($code);
        
        $response = [
            'code' => $code,
            'success' => false,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ];
        
        // 记录错误日志
        logMessage('ERROR', 'API错误响应', [
            'message' => $message,
            'code' => $code,
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'ip' => getClientIP(),
            'user_agent' => getUserAgent()
        ]);
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * 分页数据响应
     * 
     * @param array $items 数据项
     * @param int $total 总数量
     * @param int $page 当前页
     * @param int $limit 每页数量
     * @param string $message 响应消息
     * @return void
     */
    protected function paginatedResponse($items, $total, $page, $limit, $message = '获取成功') {
        $data = [
            'items' => $items,
            'pagination' => [
                'total' => intval($total),
                'page' => intval($page),
                'limit' => intval($limit),
                'pages' => ceil($total / $limit)
            ]
        ];
        
        $this->successResponse($data, $message);
    }
    
    /**
     * 验证管理员权限
     * 
     * @return void
     */
    protected function requireAdminAuth() {
        $this->validateAuth(true);
        
        // 检查是否为管理员
        if (!isset($this->currentUser['is_admin']) || !$this->currentUser['is_admin']) {
            $this->errorResponse('需要管理员权限', API_FORBIDDEN_CODE);
        }
    }
    
    /**
     * 安全的数据清理
     * 
     * @param mixed $data 需要清理的数据
     * @return mixed 清理后的数据
     */
    protected function sanitizeData($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeData'], $data);
        } elseif (is_string($data)) {
            return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        
        return $data;
    }
    
    /**
     * 抽象方法：处理请求
     * 子类必须实现此方法
     * 
     * @return void
     */
    abstract public function handleRequest();
}
?>