<?php
/**
 * 用户模型类
 * 处理用户相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

require_once __DIR__ . '/../classes/BaseModel.php';

class User extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'users';
    
    /**
     * 可填充字段
     * @var array
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'real_name',
        'phone',
        'avatar',
        'status',
        'last_login',
        'last_ip',
        'login_count',
        'reason',
        'approved_by',
        'approved_at',
        'banned_reason',
        'banned_by',
        'banned_at'
    ];
    
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'password'
    ];
    
    /**
     * 根据用户名查找用户
     * 
     * @param string $username 用户名
     * @return array|null
     */
    public function findByUsername($username)
    {
        $sql = "SELECT * FROM {$this->table} WHERE username = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$username]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $this->hideFields($result) : null;
    }
    
    /**
     * 根据邮箱查找用户
     * 
     * @param string $email 邮箱
     * @return array|null
     */
    public function findByEmail($email)
    {
        $sql = "SELECT * FROM {$this->table} WHERE email = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$email]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $this->hideFields($result) : null;
    }
    
    /**
     * 检查用户名是否存在
     * 
     * @param string $username 用户名
     * @param int $excludeId 排除的用户ID
     * @return bool
     */
    public function usernameExists($username, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * 检查邮箱是否存在
     * 
     * @param string $email 邮箱
     * @param int $excludeId 排除的用户ID
     * @return bool
     */
    public function emailExists($email, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * 获取活跃用户列表
     * 
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function getActiveUsers($limit = 20, $offset = 0)
    {
        return $this->findAll(
            ['*'],
            ['status' => 'active'],
            'created_at DESC',
            $limit,
            $offset
        );
    }
    
    /**
     * 获取待审批用户列表
     * 
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function getPendingUsers($limit = 20, $offset = 0)
    {
        return $this->findAll(
            ['*'],
            ['status' => 'pending'],
            'created_at ASC',
            $limit,
            $offset
        );
    }
    
    /**
     * 更新用户状态
     * 
     * @param int $userId 用户ID
     * @param string $status 状态
     * @param int $operatorId 操作者ID
     * @return bool
     */
    public function updateStatus($userId, $status, $operatorId = null)
    {
        $data = ['status' => $status];
        
        if ($status === 'active' && $operatorId) {
            $data['approved_by'] = $operatorId;
            $data['approved_at'] = date('Y-m-d H:i:s');
        } elseif ($status === 'banned' && $operatorId) {
            $data['banned_by'] = $operatorId;
            $data['banned_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->update($userId, $data);
    }
    
    /**
     * 更新登录信息
     * 
     * @param int $userId 用户ID
     * @param string $ip IP地址
     * @return bool
     */
    public function updateLoginInfo($userId, $ip)
    {
        return $this->update($userId, [
            'last_login' => date('Y-m-d H:i:s'),
            'last_ip' => $ip,
            'login_count' => $this->conn->prepare("login_count + 1")
        ]);
    }
    
    /**
     * 获取用户统计信息
     * 
     * @return array
     */
    public function getStatistics()
    {
        $stats = [];
        
        // 总用户数
        $stats['total'] = $this->count();
        
        // 活跃用户数
        $stats['active'] = $this->count(['status' => 'active']);
        
        // 待审批用户数
        $stats['pending'] = $this->count(['status' => 'pending']);
        
        // 被封禁用户数
        $stats['banned'] = $this->count(['status' => 'banned']);
        
        // 今日注册用户数
        $today = date('Y-m-d');
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE DATE(created_at) = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$today]);
        $stats['today_registered'] = $stmt->fetchColumn();
        
        // 本月注册用户数
        $thisMonth = date('Y-m');
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE DATE_FORMAT(created_at, '%Y-%m') = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$thisMonth]);
        $stats['month_registered'] = $stmt->fetchColumn();
        
        return $stats;
    }
    
    /**
     * 搜索用户
     * 
     * @param string $keyword 关键词
     * @param array $filters 过滤条件
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function search($keyword = '', $filters = [], $limit = 20, $offset = 0)
    {
        $sql = "SELECT * FROM {$this->table} WHERE 1=1";
        $params = [];
        
        // 关键词搜索
        if (!empty($keyword)) {
            $sql .= " AND (username LIKE ? OR email LIKE ? OR real_name LIKE ?)";
            $likeKeyword = "%{$keyword}%";
            $params[] = $likeKeyword;
            $params[] = $likeKeyword;
            $params[] = $likeKeyword;
        }
        
        // 状态过滤
        if (!empty($filters['status'])) {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        // 日期范围过滤
        if (!empty($filters['date_from'])) {
            $sql .= " AND created_at >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }
        
        // 排序
        $sql .= " ORDER BY created_at DESC";
        
        // 分页
        if ($limit > 0) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 批量更新用户状态
     * 
     * @param array $userIds 用户ID数组
     * @param string $status 状态
     * @param int $operatorId 操作者ID
     * @return bool
     */
    public function batchUpdateStatus($userIds, $status, $operatorId = null)
    {
        if (empty($userIds)) {
            return false;
        }
        
        $this->beginTransaction();
        
        try {
            foreach ($userIds as $userId) {
                $this->updateStatus($userId, $status, $operatorId);
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }
}
