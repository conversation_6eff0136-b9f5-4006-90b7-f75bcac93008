<?php
/**
 * 用户登录API控制器
 * 基于BaseController重构，提供统一的API处理机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @package API\Auth
 * @since 2024-01-01
 * 
 * @api {POST} /api/auth/login.php 用户登录
 * @apiName UserLogin
 * @apiGroup Authentication
 * @apiVersion 1.0.0
 * 
 * @apiParam {String} username 用户名 (必填)
 * @apiParam {String} password 密码 (必填)
 * 
 * @apiSuccess {Number} code 状态码 (200)
 * @apiSuccess {Boolean} success 是否成功 (true)
 * @apiSuccess {String} message 响应消息
 * @apiSuccess {Object} data 响应数据
 * @apiSuccess {String} data.token JWT认证令牌
 * @apiSuccess {Object} data.user 用户信息
 * @apiSuccess {Number} data.user.id 用户ID
 * @apiSuccess {String} data.user.username 用户名
 * @apiSuccess {String} data.user.email 邮箱地址
 * @apiSuccess {String} data.user.real_name 真实姓名
 * @apiSuccess {String} data.user.last_login 最后登录时间
 * @apiSuccess {Number} timestamp 时间戳
 * 
 * @apiError {Number} code 错误状态码
 * @apiError {Boolean} success 是否成功 (false)
 * @apiError {String} message 错误消息
 * @apiError {Number} timestamp 时间戳
 * 
 * @apiExample {json} 请求示例:
 * {
 *   "username": "testuser",
 *   "password": "password123"
 * }
 * 
 * @apiExample {json} 成功响应示例:
 * {
 *   "code": 200,
 *   "success": true,
 *   "message": "登录成功",
 *   "data": {
 *     "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
 *     "user": {
 *       "id": 1,
 *       "username": "testuser",
 *       "email": "<EMAIL>",
 *       "real_name": "测试用户",
 *       "last_login": "2024-01-01 12:00:00"
 *     }
 *   },
 *   "timestamp": 1234567890
 * }
 */

// 引入基础控制器和相关类
require_once __DIR__ . '/../../includes/BaseController.php';
require_once __DIR__ . '/../../includes/ValidationHelper.php';
require_once __DIR__ . '/../../includes/auth.php';

/**
 * 登录API控制器类
 * 
 * @extends BaseController
 */
class LoginController extends BaseController {
    
    /**
     * 处理登录请求
     * 
     * @return void
     */
    public function handleRequest() {
        // 验证请求方法
        $this->validateRequestMethod('POST');
        
        // 验证并清理输入数据
        $validationResult = $this->validateLoginInput();
        if (!$validationResult['valid']) {
            $this->errorResponse(
                '输入验证失败: ' . implode(', ', $validationResult['errors']), 
                API_ERROR_CODE
            );
        }
        
        $cleanData = $validationResult['data'];
        
        try {
            // 执行登录操作
            $loginResult = $this->performLogin($cleanData['username'], $cleanData['password']);
            
            if ($loginResult['success']) {
                // 记录成功登录
                $this->logOperation('user_login', '用户登录成功', [
                    'username' => $cleanData['username'],
                    'user_id' => $loginResult['user']['id']
                ]);
                
                // 返回成功响应
                $this->successResponse([
                    'token' => $loginResult['token'],
                    'user' => $this->sanitizeUserData($loginResult['user'])
                ], $loginResult['message']);
            } else {
                // 记录登录失败
                $this->logOperation('login_failed', '用户登录失败', [
                    'username' => $cleanData['username'],
                    'reason' => $loginResult['message']
                ]);
                
                $this->errorResponse($loginResult['message'], API_UNAUTHORIZED_CODE);
            }
            
        } catch (Exception $e) {
            // 记录异常
            logMessage('ERROR', 'API登录异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'username' => $cleanData['username'],
                'ip' => getClientIP(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->errorResponse('登录处理异常，请稍后重试', API_SERVER_ERROR_CODE);
        }
    }
    
    /**
     * 验证登录输入数据
     * 
     * @return array 验证结果
     */
    private function validateLoginInput() {
        // 定义验证规则
        $validationRules = [
            'username' => 'required|min_length:3|max_length:20|username',
            'password' => 'required|min_length:6|max_length:50'
        ];
        
        // 定义清理规则
        $sanitizeRules = [
            'username' => ['trim', 'lowercase'],
            'password' => ['trim']
        ];
        
        return ValidationHelper::validateAndSanitize($this->input, $validationRules, $sanitizeRules);
    }
    
    /**
     * 执行登录操作
     * 
     * @param string $username 用户名
     * @param string $password 密码
     * @return array 登录结果
     * @throws Exception 当登录处理失败时
     */
    private function performLogin($username, $password) {
        $auth = new Auth();
        return $auth->login($username, $password);
    }
    
    /**
     * 清理用户数据（移除敏感信息）
     * 
     * @param array $userData 原始用户数据
     * @return array 清理后的用户数据
     */
    private function sanitizeUserData($userData) {
        // 移除敏感字段
        $sensitiveFields = ['password', 'salt', 'login_attempts', 'banned_reason'];
        
        foreach ($sensitiveFields as $field) {
            unset($userData[$field]);
        }
        
        // 确保数据安全
        return $this->sanitizeData($userData);
    }
}

// 实例化并处理请求
try {
    $controller = new LoginController();
    $controller->handleRequest();
} catch (Exception $e) {
    // 最后的异常处理
    header('Content-Type: application/json; charset=utf-8');
    http_response_code(500);
    
    echo json_encode([
        'code' => 500,
        'success' => false,
        'message' => '服务器内部错误',
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    
    // 记录致命错误
    logMessage('CRITICAL', '登录API致命错误', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>