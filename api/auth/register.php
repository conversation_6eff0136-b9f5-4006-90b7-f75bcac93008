<?php
/**
 * 用户注册API
 * 管理系统 - management.djxs.xyz
 * 
 * 接口说明：
 * POST /api/auth/register.php
 * 
 * 请求参数：
 * - username: 用户名 (必填, 3-20字符)
 * - password: 密码 (必填, 最少6字符)
 * - email: 邮箱 (必填)
 * - verification_code: 邮箱验证码 (必填)
 * - phone: 手机号 (可选)
 * - real_name: 真实姓名 (可选)
 * - reason: 申请理由 (可选)
 * 
 * 返回格式：
 * {
 *   "code": 200,
 *   "message": "注册申请已提交，请等待管理员审批",
 *   "data": null,
 *   "timestamp": 1234567890
 * }
 */

// 引入必要文件
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/BaseController.php';
require_once __DIR__ . '/../../includes/auth.php';

/**
 * 用户注册控制器
 */
class RegisterController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->requireAuth = false; // 注册接口不需要认证
        $this->allowedMethods = ['POST', 'OPTIONS'];
        parent::__construct();
    }

    /**
     * 处理注册请求
     *
     * @return void
     */
    public function handle()
    {
        try {
            // 验证请求数据
            $data = $this->validateRequest([
                'username' => 'required|min_length:3|max_length:20|regex:/^[a-zA-Z0-9_]+$/',
                'password' => 'required|min_length:6',
                'email' => 'required|email',
                'verification_code' => 'required|min_length:4|max_length:10',
                'phone' => 'phone',
                'real_name' => 'max_length:50',
                'reason' => 'max_length:500'
            ], [
                'username.required' => '用户名不能为空',
                'username.min_length' => '用户名至少3个字符',
                'username.max_length' => '用户名不能超过20个字符',
                'username.regex' => '用户名只能包含字母、数字和下划线',
                'password.required' => '密码不能为空',
                'password.min_length' => '密码至少6个字符',
                'email.required' => '邮箱不能为空',
                'email.email' => '邮箱格式不正确',
                'verification_code.required' => '验证码不能为空',
                'phone.phone' => '手机号格式不正确',
                'real_name.max_length' => '真实姓名不能超过50个字符',
                'reason.max_length' => '申请理由不能超过500个字符'
            ]);

            // 创建认证实例
            $auth = new Auth();

            // 执行注册
            $result = $auth->register(
                $data['username'],
                $data['password'],
                $data['email'],
                $data['phone'] ?? '',
                $data['real_name'] ?? '',
                $data['reason'] ?? '',
                $data['verification_code']
            );

            if ($result['success']) {
                // 记录注册日志
                $this->logOperation('user_register', '用户注册申请', [
                    'username' => $data['username'],
                    'email' => $data['email'],
                    'ip' => $this->getClientIP()
                ]);

                ResponseHelper::success(null, $result['message']);
            } else {
                ResponseHelper::error($result['message'] ?? '注册失败');
            }

        } catch (Exception $e) {
            // 记录错误日志
            error_log('用户注册错误: ' . $e->getMessage());
            ResponseHelper::error($e->getMessage());
        }
    }
}

// 创建控制器实例并处理请求
$controller = new RegisterController();
$controller->handle();
?>
