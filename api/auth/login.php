<?php
/**
 * 用户登录API
 * 管理系统 - management.djxs.xyz
 * 
 * 接口说明：
 * POST /api/auth/login.php
 * 
 * 请求参数：
 * - username: 用户名 (必填)
 * - password: 密码 (必填)
 * 
 * 返回格式：
 * {
 *   "code": 200,
 *   "message": "登录成功",
 *   "data": {
 *     "token": "用户token",
 *     "user": {
 *       "id": 1,
 *       "username": "testuser",
 *       "email": "<EMAIL>",
 *       "real_name": "测试用户",
 *       "last_login": "2024-01-01 12:00:00"
 *     }
 *   },
 *   "timestamp": 1234567890
 * }
 */

// 引入必要文件
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/BaseController.php';
require_once __DIR__ . '/../../includes/auth.php';

/**
 * 用户登录控制器
 */
class LoginController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->requireAuth = false; // 登录接口不需要认证
        $this->allowedMethods = ['POST', 'OPTIONS'];
        parent::__construct();
    }

    /**
     * 处理登录请求
     *
     * @return void
     */
    public function handle()
    {
        try {
            // 验证请求数据
            $data = $this->validateRequest([
                'username' => 'required|min_length:3|max_length:50',
                'password' => 'required|min_length:6'
            ], [
                'username.required' => '用户名不能为空',
                'username.min_length' => '用户名至少3个字符',
                'password.required' => '密码不能为空',
                'password.min_length' => '密码至少6个字符'
            ]);

            // 创建认证实例
            $auth = new Auth();

            // 执行登录
            $result = $auth->login($data['username'], $data['password']);

            if ($result['success']) {
                // 记录登录日志
                $this->logOperation('user_login', '用户登录成功', [
                    'username' => $data['username'],
                    'ip' => $this->getClientIP()
                ]);

                ResponseHelper::success([
                    'token' => $result['token'],
                    'user' => $result['user']
                ], $result['message']);
            } else {
                ResponseHelper::unauthorized($result['message'] ?? '登录失败');
            }

        } catch (Exception $e) {
            // 记录错误日志
            error_log('用户登录错误: ' . $e->getMessage());
            ResponseHelper::error($e->getMessage());
        }
    }
}

// 创建控制器实例并处理请求
$controller = new LoginController();
$controller->handle();
?>
