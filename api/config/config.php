<?php
/**
 * 全局配置文件
 * 管理系统 - management.djxs.xyz
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/error.log');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 系统配置
define('SYSTEM_NAME', '管理系统');
define('SYSTEM_VERSION', '1.0.0');
define('DOMAIN', 'management.djxs.xyz');
define('BASE_URL', 'https://' . DOMAIN);
define('API_BASE_URL', BASE_URL . '/api');

// 安全配置
define('JWT_SECRET_KEY', 'management_djxs_xyz_secret_key_2024_' . md5(DOMAIN));
define('TOKEN_EXPIRE_TIME', 7200); // 2小时
define('PASSWORD_SALT', 'management_salt_' . md5(DOMAIN));

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'management');
define('DB_USER', 'management');
define('DB_PASS', 'bt3YBkRR3rfeXY5j');
define('DB_CHARSET', 'utf8mb4');

// API响应配置
define('API_SUCCESS_CODE', 200);
define('API_ERROR_CODE', 400);
define('API_UNAUTHORIZED_CODE', 401);
define('API_FORBIDDEN_CODE', 403);
define('API_NOT_FOUND_CODE', 404);
define('API_SERVER_ERROR_CODE', 500);

// 文件上传配置
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_PATH', __DIR__ . '/../../uploads/');

// 日志配置
define('LOG_PATH', __DIR__ . '/../../logs/');
define('LOG_LEVEL', 'INFO');

// 邮件配置（如需要）
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', 'noreply@' . DOMAIN);
define('SMTP_FROM_NAME', SYSTEM_NAME);

// 创建必要的目录
$directories = [
    __DIR__ . '/../../logs',
    __DIR__ . '/../../uploads',
    __DIR__ . '/../../uploads/avatars'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 通用函数
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

function hashPassword($password) {
    return password_hash($password . PASSWORD_SALT, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password . PASSWORD_SALT, $hash);
}

function getClientIP() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

function getUserAgent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? '';
}

function logMessage($level, $message, $context = []) {
    $logFile = LOG_PATH . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
    $logEntry = "[{$timestamp}] [{$level}] {$message}{$contextStr}" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// CORS设置
function setCORSHeaders() {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    header('Access-Control-Max-Age: 86400');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// 自动加载公共类
spl_autoload_register(function ($className) {
    $classFile = __DIR__ . '/../classes/' . $className . '.php';
    if (file_exists($classFile)) {
        require_once $classFile;
    }
});

// JSON响应函数（保留向后兼容性）
function jsonResponse($data, $code = 200, $message = '') {
    ResponseHelper::json($data, $code, $message);
}

function successResponse($data = null, $message = '操作成功') {
    ResponseHelper::success($data, $message);
}

function errorResponse($message = '操作失败', $code = API_ERROR_CODE, $data = null) {
    ResponseHelper::error($message, $code, $data);
}
?>
