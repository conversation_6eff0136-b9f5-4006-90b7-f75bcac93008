<?php
/**
 * 数据库配置和连接管理类
 * 管理系统 - management.djxs.xyz
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-28
 */

class Database
{
    /**
     * 数据库配置
     */
    private $host = 'localhost';
    private $db_name = 'management';
    private $username = 'management';
    private $password = 'bt3YBkRR3rfeXY5j';
    private $charset = 'utf8mb4';

    /**
     * 连接实例
     * @var PDO|null
     */
    public $conn = null;

    /**
     * 单例实例
     * @var Database|null
     */
    private static $instance = null;

    /**
     * 连接池
     * @var array
     */
    private static $connectionPool = [];

    /**
     * 最大连接数
     * @var int
     */
    private static $maxConnections = 10;

    /**
     * 当前连接数
     * @var int
     */
    private static $currentConnections = 0;

    /**
     * 事务嵌套级别
     * @var int
     */
    private $transactionLevel = 0;

    /**
     * 构造函数（私有，单例模式）
     */
    private function __construct() {}

    /**
     * 获取单例实例
     *
     * @return Database
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 获取数据库连接
     *
     * @param bool $forceNew 是否强制创建新连接
     * @return PDO
     * @throws Exception
     */
    public function getConnection($forceNew = false)
    {
        // 如果已有连接且不强制创建新连接，直接返回
        if ($this->conn && !$forceNew) {
            return $this->conn;
        }

        // 尝试从连接池获取连接
        if (!$forceNew && !empty(self::$connectionPool)) {
            $this->conn = array_pop(self::$connectionPool);
            if ($this->isConnectionAlive($this->conn)) {
                return $this->conn;
            }
        }

        // 检查连接数限制
        if (self::$currentConnections >= self::$maxConnections) {
            throw new Exception("数据库连接数已达上限");
        }

        try {
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
                PDO::ATTR_PERSISTENT => false, // 禁用持久连接以便更好地管理连接池
                PDO::ATTR_TIMEOUT => 30, // 连接超时时间
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            self::$currentConnections++;

            // 设置SQL模式和时区
            $this->conn->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
            $this->conn->exec("SET time_zone = '+08:00'");

        } catch (PDOException $exception) {
            error_log("数据库连接错误: " . $exception->getMessage());
            throw new Exception("数据库连接失败: " . $exception->getMessage());
        }

        return $this->conn;
    }

    /**
     * 检查连接是否存活
     *
     * @param PDO $connection 数据库连接
     * @return bool
     */
    private function isConnectionAlive($connection)
    {
        try {
            $connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * 释放连接到连接池
     *
     * @return void
     */
    public function releaseConnection()
    {
        if ($this->conn && $this->transactionLevel === 0) {
            // 只有在没有活跃事务时才释放连接
            if (count(self::$connectionPool) < self::$maxConnections) {
                self::$connectionPool[] = $this->conn;
            } else {
                self::$currentConnections--;
            }
            $this->conn = null;
        }
    }

    /**
     * 关闭数据库连接
     *
     * @return void
     */
    public function closeConnection()
    {
        if ($this->conn) {
            self::$currentConnections--;
            $this->conn = null;
        }
    }

    /**
     * 开始事务（支持嵌套事务）
     *
     * @return bool
     * @throws Exception
     */
    public function beginTransaction()
    {
        $conn = $this->getConnection();

        if ($this->transactionLevel === 0) {
            $result = $conn->beginTransaction();
        } else {
            // 嵌套事务使用保存点
            $savepointName = 'savepoint_' . $this->transactionLevel;
            $result = $conn->exec("SAVEPOINT {$savepointName}");
        }

        $this->transactionLevel++;
        return $result !== false;
    }

    /**
     * 提交事务
     *
     * @return bool
     * @throws Exception
     */
    public function commit()
    {
        if ($this->transactionLevel === 0) {
            throw new Exception("没有活跃的事务可以提交");
        }

        $conn = $this->getConnection();
        $this->transactionLevel--;

        if ($this->transactionLevel === 0) {
            return $conn->commit();
        } else {
            // 嵌套事务释放保存点
            $savepointName = 'savepoint_' . $this->transactionLevel;
            return $conn->exec("RELEASE SAVEPOINT {$savepointName}") !== false;
        }
    }

    /**
     * 回滚事务
     *
     * @return bool
     * @throws Exception
     */
    public function rollback()
    {
        if ($this->transactionLevel === 0) {
            throw new Exception("没有活跃的事务可以回滚");
        }

        $conn = $this->getConnection();
        $this->transactionLevel--;

        if ($this->transactionLevel === 0) {
            return $conn->rollback();
        } else {
            // 嵌套事务回滚到保存点
            $savepointName = 'savepoint_' . $this->transactionLevel;
            return $conn->exec("ROLLBACK TO SAVEPOINT {$savepointName}") !== false;
        }
    }

    /**
     * 获取事务级别
     *
     * @return int
     */
    public function getTransactionLevel()
    {
        return $this->transactionLevel;
    }

    /**
     * 测试数据库连接
     *
     * @return bool
     */
    public function testConnection()
    {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->query('SELECT 1');
            return $stmt !== false;
        } catch (Exception $e) {
            error_log("数据库连接测试失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取数据库信息
     *
     * @return array
     */
    public function getDatabaseInfo()
    {
        try {
            $conn = $this->getConnection();

            $info = [
                'server_version' => $conn->getAttribute(PDO::ATTR_SERVER_VERSION),
                'client_version' => $conn->getAttribute(PDO::ATTR_CLIENT_VERSION),
                'connection_status' => $conn->getAttribute(PDO::ATTR_CONNECTION_STATUS),
                'server_info' => $conn->getAttribute(PDO::ATTR_SERVER_INFO),
                'current_connections' => self::$currentConnections,
                'pool_size' => count(self::$connectionPool),
                'transaction_level' => $this->transactionLevel
            ];

            return $info;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 清理连接池
     *
     * @return void
     */
    public static function clearConnectionPool()
    {
        foreach (self::$connectionPool as $conn) {
            $conn = null;
        }
        self::$connectionPool = [];
        self::$currentConnections = 0;
    }

    /**
     * 执行SQL查询（带重试机制）
     *
     * @param string $sql SQL语句
     * @param array $params 参数
     * @param int $maxRetries 最大重试次数
     * @return PDOStatement
     * @throws Exception
     */
    public function executeQuery($sql, $params = [], $maxRetries = 3)
    {
        $retries = 0;

        while ($retries < $maxRetries) {
            try {
                $conn = $this->getConnection();
                $stmt = $conn->prepare($sql);
                $stmt->execute($params);
                return $stmt;

            } catch (PDOException $e) {
                $retries++;

                // 如果是连接丢失错误，尝试重新连接
                if (strpos($e->getMessage(), 'server has gone away') !== false ||
                    strpos($e->getMessage(), 'Lost connection') !== false) {

                    $this->closeConnection();

                    if ($retries < $maxRetries) {
                        usleep(100000); // 等待100ms后重试
                        continue;
                    }
                }

                throw $e;
            }
        }

        throw new Exception("SQL执行失败，已重试{$maxRetries}次");
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->releaseConnection();
    }
}
?>
