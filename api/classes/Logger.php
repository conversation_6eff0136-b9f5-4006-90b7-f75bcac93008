<?php
/**
 * 统一日志系统类
 * 提供标准化的日志记录和管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

class Logger
{
    /**
     * 日志级别常量
     */
    const EMERGENCY = 'emergency';  // 系统不可用
    const ALERT     = 'alert';      // 必须立即采取行动
    const CRITICAL  = 'critical';   // 严重错误
    const ERROR     = 'error';      // 运行时错误
    const WARNING   = 'warning';    // 警告信息
    const NOTICE    = 'notice';     // 一般重要信息
    const INFO      = 'info';       // 一般信息
    const DEBUG     = 'debug';      // 调试信息
    
    /**
     * 日志级别优先级映射
     * @var array
     */
    private static $levels = [
        self::EMERGENCY => 0,
        self::ALERT     => 1,
        self::CRITICAL  => 2,
        self::ERROR     => 3,
        self::WARNING   => 4,
        self::NOTICE    => 5,
        self::INFO      => 6,
        self::DEBUG     => 7
    ];
    
    /**
     * 日志配置
     * @var array
     */
    private static $config = [
        'log_path' => null,
        'max_file_size' => 10485760, // 10MB
        'max_files' => 10,
        'date_format' => 'Y-m-d H:i:s',
        'log_format' => '[{datetime}] {level}: {message} {context}',
        'min_level' => self::DEBUG,
        'enable_rotation' => true,
        'enable_console' => false
    ];
    
    /**
     * 单例实例
     * @var Logger|null
     */
    private static $instance = null;
    
    /**
     * 日志处理器
     * @var array
     */
    private $handlers = [];
    
    /**
     * 构造函数（私有，单例模式）
     */
    private function __construct()
    {
        // 初始化配置
        if (defined('LOG_PATH')) {
            self::$config['log_path'] = LOG_PATH;
        } else {
            self::$config['log_path'] = __DIR__ . '/../../logs/';
        }
        
        if (defined('LOG_LEVEL')) {
            self::$config['min_level'] = LOG_LEVEL;
        }
        
        // 确保日志目录存在
        if (!is_dir(self::$config['log_path'])) {
            mkdir(self::$config['log_path'], 0755, true);
        }
    }
    
    /**
     * 获取单例实例
     * 
     * @return Logger
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 记录紧急日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function emergency($message, array $context = [])
    {
        self::getInstance()->log(self::EMERGENCY, $message, $context);
    }
    
    /**
     * 记录警报日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function alert($message, array $context = [])
    {
        self::getInstance()->log(self::ALERT, $message, $context);
    }
    
    /**
     * 记录严重错误日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function critical($message, array $context = [])
    {
        self::getInstance()->log(self::CRITICAL, $message, $context);
    }
    
    /**
     * 记录错误日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function error($message, array $context = [])
    {
        self::getInstance()->log(self::ERROR, $message, $context);
    }
    
    /**
     * 记录警告日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function warning($message, array $context = [])
    {
        self::getInstance()->log(self::WARNING, $message, $context);
    }
    
    /**
     * 记录通知日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function notice($message, array $context = [])
    {
        self::getInstance()->log(self::NOTICE, $message, $context);
    }
    
    /**
     * 记录信息日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function info($message, array $context = [])
    {
        self::getInstance()->log(self::INFO, $message, $context);
    }
    
    /**
     * 记录调试日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function debug($message, array $context = [])
    {
        self::getInstance()->log(self::DEBUG, $message, $context);
    }
    
    /**
     * 记录日志
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public function log($level, $message, array $context = [])
    {
        // 检查日志级别
        if (!$this->shouldLog($level)) {
            return;
        }
        
        // 格式化日志消息
        $formattedMessage = $this->formatMessage($level, $message, $context);
        
        // 写入日志文件
        $this->writeToFile($formattedMessage);
        
        // 控制台输出（如果启用）
        if (self::$config['enable_console']) {
            echo $formattedMessage . PHP_EOL;
        }
        
        // 调用自定义处理器
        foreach ($this->handlers as $handler) {
            call_user_func($handler, $level, $message, $context);
        }
    }
    
    /**
     * 检查是否应该记录该级别的日志
     * 
     * @param string $level 日志级别
     * @return bool
     */
    private function shouldLog($level)
    {
        $currentLevelPriority = self::$levels[$level] ?? 7;
        $minLevelPriority = self::$levels[self::$config['min_level']] ?? 7;
        
        return $currentLevelPriority <= $minLevelPriority;
    }
    
    /**
     * 格式化日志消息
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return string 格式化后的消息
     */
    private function formatMessage($level, $message, array $context = [])
    {
        $datetime = date(self::$config['date_format']);
        $contextString = empty($context) ? '' : json_encode($context, JSON_UNESCAPED_UNICODE);
        
        // 替换占位符
        $formatted = str_replace([
            '{datetime}',
            '{level}',
            '{message}',
            '{context}'
        ], [
            $datetime,
            strtoupper($level),
            $message,
            $contextString
        ], self::$config['log_format']);
        
        return $formatted;
    }
    
    /**
     * 写入日志文件
     * 
     * @param string $message 格式化后的日志消息
     * @return void
     */
    private function writeToFile($message)
    {
        $logFile = self::$config['log_path'] . date('Y-m-d') . '.log';
        
        // 检查文件大小，如果需要则轮转
        if (self::$config['enable_rotation'] && file_exists($logFile)) {
            $fileSize = filesize($logFile);
            if ($fileSize >= self::$config['max_file_size']) {
                $this->rotateLogFile($logFile);
            }
        }
        
        // 写入日志
        file_put_contents($logFile, $message . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 轮转日志文件
     * 
     * @param string $logFile 日志文件路径
     * @return void
     */
    private function rotateLogFile($logFile)
    {
        $maxFiles = self::$config['max_files'];
        
        // 删除最旧的日志文件
        $oldestFile = $logFile . '.' . $maxFiles;
        if (file_exists($oldestFile)) {
            unlink($oldestFile);
        }
        
        // 重命名现有文件
        for ($i = $maxFiles - 1; $i >= 1; $i--) {
            $oldFile = $logFile . '.' . $i;
            $newFile = $logFile . '.' . ($i + 1);
            
            if (file_exists($oldFile)) {
                rename($oldFile, $newFile);
            }
        }
        
        // 重命名当前文件
        rename($logFile, $logFile . '.1');
    }
    
    /**
     * 添加自定义日志处理器
     * 
     * @param callable $handler 处理器函数
     * @return void
     */
    public function addHandler(callable $handler)
    {
        $this->handlers[] = $handler;
    }
    
    /**
     * 设置配置
     * 
     * @param array $config 配置数组
     * @return void
     */
    public static function setConfig(array $config)
    {
        self::$config = array_merge(self::$config, $config);
    }
    
    /**
     * 获取配置
     * 
     * @param string $key 配置键名
     * @return mixed 配置值
     */
    public static function getConfig($key = null)
    {
        if ($key === null) {
            return self::$config;
        }
        
        return self::$config[$key] ?? null;
    }
    
    /**
     * 清理旧日志文件
     * 
     * @param int $days 保留天数
     * @return int 删除的文件数量
     */
    public static function cleanOldLogs($days = 30)
    {
        $logPath = self::$config['log_path'];
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $deletedCount = 0;
        
        if (is_dir($logPath)) {
            $files = glob($logPath . '*.log*');
            
            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $deletedCount++;
                }
            }
        }
        
        return $deletedCount;
    }
}
