<?php
/**
 * 基础模型类
 * 提供通用的数据库操作方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

require_once __DIR__ . '/../config/database.php';

abstract class BaseModel
{
    /**
     * 数据库连接实例
     * @var PDO
     */
    protected $conn;
    
    /**
     * 数据库实例
     * @var Database
     */
    protected $database;
    
    /**
     * 表名
     * @var string
     */
    protected $table;
    
    /**
     * 主键字段名
     * @var string
     */
    protected $primaryKey = 'id';
    
    /**
     * 可填充字段
     * @var array
     */
    protected $fillable = [];
    
    /**
     * 隐藏字段（不在查询结果中显示）
     * @var array
     */
    protected $hidden = [];
    
    /**
     * 时间戳字段
     * @var array
     */
    protected $timestamps = ['created_at', 'updated_at'];
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->database = new Database();
        $this->conn = $this->database->getConnection();
    }
    
    /**
     * 查找单条记录
     * 
     * @param mixed $id 主键值
     * @param array $columns 查询字段
     * @return array|null
     */
    public function find($id, $columns = ['*'])
    {
        $sql = "SELECT " . implode(', ', $columns) . " FROM {$this->table} WHERE {$this->primaryKey} = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $this->hideFields($result) : null;
    }
    
    /**
     * 查找所有记录
     * 
     * @param array $columns 查询字段
     * @param array $conditions 查询条件
     * @param string $orderBy 排序
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function findAll($columns = ['*'], $conditions = [], $orderBy = null, $limit = null, $offset = null)
    {
        $sql = "SELECT " . implode(', ', $columns) . " FROM {$this->table}";
        $params = [];
        
        // 添加查询条件
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                if (is_array($value)) {
                    $placeholders = str_repeat('?,', count($value) - 1) . '?';
                    $whereClause[] = "{$field} IN ({$placeholders})";
                    $params = array_merge($params, $value);
                } else {
                    $whereClause[] = "{$field} = ?";
                    $params[] = $value;
                }
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        // 添加排序
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        // 添加限制
        if ($limit) {
            $sql .= " LIMIT {$limit}";
            if ($offset) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 创建记录
     * 
     * @param array $data 数据
     * @return int|false 插入的ID或false
     */
    public function create($data)
    {
        // 过滤可填充字段
        $data = $this->filterFillable($data);
        
        // 添加时间戳
        if (in_array('created_at', $this->timestamps)) {
            $data['created_at'] = date('Y-m-d H:i:s');
        }
        if (in_array('updated_at', $this->timestamps)) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $fields = array_keys($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";
        $stmt = $this->conn->prepare($sql);
        
        if ($stmt->execute(array_values($data))) {
            return $this->conn->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * 更新记录
     * 
     * @param mixed $id 主键值
     * @param array $data 更新数据
     * @return bool
     */
    public function update($id, $data)
    {
        // 过滤可填充字段
        $data = $this->filterFillable($data);
        
        // 添加更新时间戳
        if (in_array('updated_at', $this->timestamps)) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = ?";
        $params = array_merge(array_values($data), [$id]);
        
        $stmt = $this->conn->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * 删除记录
     * 
     * @param mixed $id 主键值
     * @return bool
     */
    public function delete($id)
    {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        $stmt = $this->conn->prepare($sql);
        return $stmt->execute([$id]);
    }
    
    /**
     * 软删除记录
     * 
     * @param mixed $id 主键值
     * @return bool
     */
    public function softDelete($id)
    {
        return $this->update($id, ['deleted_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 统计记录数量
     * 
     * @param array $conditions 查询条件
     * @return int
     */
    public function count($conditions = [])
    {
        $sql = "SELECT COUNT(*) FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        return (int)$stmt->fetchColumn();
    }
    
    /**
     * 检查记录是否存在
     * 
     * @param array $conditions 查询条件
     * @return bool
     */
    public function exists($conditions)
    {
        return $this->count($conditions) > 0;
    }
    
    /**
     * 执行原生SQL查询
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array
     */
    public function query($sql, $params = [])
    {
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * 开始事务
     * 
     * @return void
     */
    public function beginTransaction()
    {
        $this->conn->beginTransaction();
    }
    
    /**
     * 提交事务
     * 
     * @return void
     */
    public function commit()
    {
        $this->conn->commit();
    }
    
    /**
     * 回滚事务
     * 
     * @return void
     */
    public function rollback()
    {
        $this->conn->rollback();
    }
    
    /**
     * 过滤可填充字段
     * 
     * @param array $data 原始数据
     * @return array 过滤后的数据
     */
    protected function filterFillable($data)
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    /**
     * 隐藏指定字段
     * 
     * @param array $data 原始数据
     * @return array 处理后的数据
     */
    protected function hideFields($data)
    {
        if (empty($this->hidden)) {
            return $data;
        }
        
        return array_diff_key($data, array_flip($this->hidden));
    }
    
    /**
     * 获取表名
     * 
     * @return string
     */
    public function getTable()
    {
        return $this->table;
    }
    
    /**
     * 获取数据库连接
     * 
     * @return PDO
     */
    public function getConnection()
    {
        return $this->conn;
    }
}
