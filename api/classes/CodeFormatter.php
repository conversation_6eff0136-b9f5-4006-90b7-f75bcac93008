<?php
/**
 * 代码格式化助手类
 * 统一代码格式和命名规范
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

class CodeFormatter
{
    /**
     * PSR-4 命名规范检查
     * 
     * @param string $className 类名
     * @return bool
     */
    public static function validateClassName($className)
    {
        // 类名应该使用 PascalCase
        return preg_match('/^[A-Z][a-zA-Z0-9]*$/', $className);
    }
    
    /**
     * 方法名命名规范检查
     * 
     * @param string $methodName 方法名
     * @return bool
     */
    public static function validateMethodName($methodName)
    {
        // 方法名应该使用 camelCase
        return preg_match('/^[a-z][a-zA-Z0-9]*$/', $methodName);
    }
    
    /**
     * 变量名命名规范检查
     * 
     * @param string $variableName 变量名
     * @return bool
     */
    public static function validateVariableName($variableName)
    {
        // 变量名应该使用 camelCase 或 snake_case
        return preg_match('/^[a-z][a-zA-Z0-9]*$/', $variableName) || 
               preg_match('/^[a-z][a-z0-9_]*$/', $variableName);
    }
    
    /**
     * 常量名命名规范检查
     * 
     * @param string $constantName 常量名
     * @return bool
     */
    public static function validateConstantName($constantName)
    {
        // 常量名应该使用 UPPER_SNAKE_CASE
        return preg_match('/^[A-Z][A-Z0-9_]*$/', $constantName);
    }
    
    /**
     * 格式化PHP代码缩进
     * 
     * @param string $code PHP代码
     * @param int $indentSize 缩进大小
     * @param string $indentChar 缩进字符
     * @return string 格式化后的代码
     */
    public static function formatIndentation($code, $indentSize = 4, $indentChar = ' ')
    {
        $lines = explode("\n", $code);
        $formattedLines = [];
        $indentLevel = 0;
        $indent = str_repeat($indentChar, $indentSize);
        
        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            
            // 跳过空行
            if (empty($trimmedLine)) {
                $formattedLines[] = '';
                continue;
            }
            
            // 减少缩进的情况
            if (preg_match('/^(}|\)|case\s|default:)/', $trimmedLine)) {
                $indentLevel = max(0, $indentLevel - 1);
            }
            
            // 应用缩进
            $formattedLines[] = str_repeat($indent, $indentLevel) . $trimmedLine;
            
            // 增加缩进的情况
            if (preg_match('/[{(]$/', $trimmedLine) || 
                preg_match('/^(if|else|elseif|for|foreach|while|do|switch|case|default|try|catch|finally)\b/', $trimmedLine)) {
                $indentLevel++;
            }
        }
        
        return implode("\n", $formattedLines);
    }
    
    /**
     * 标准化注释格式
     * 
     * @param string $comment 注释内容
     * @param string $type 注释类型 (single, multi, doc)
     * @return string 格式化后的注释
     */
    public static function formatComment($comment, $type = 'single')
    {
        switch ($type) {
            case 'single':
                return '// ' . trim($comment);
                
            case 'multi':
                $lines = explode("\n", trim($comment));
                $formatted = ["/*"];
                foreach ($lines as $line) {
                    $formatted[] = ' * ' . trim($line);
                }
                $formatted[] = ' */';
                return implode("\n", $formatted);
                
            case 'doc':
                $lines = explode("\n", trim($comment));
                $formatted = ["/**"];
                foreach ($lines as $line) {
                    $formatted[] = ' * ' . trim($line);
                }
                $formatted[] = ' */';
                return implode("\n", $formatted);
                
            default:
                return $comment;
        }
    }
    
    /**
     * 生成标准的文件头注释
     * 
     * @param string $filename 文件名
     * @param string $description 文件描述
     * @param string $author 作者
     * @param string $version 版本
     * @return string 文件头注释
     */
    public static function generateFileHeader($filename, $description, $author = '管理系统', $version = '1.0.0')
    {
        $date = date('Y-m-d');
        
        return "<?php\n/**\n" .
               " * {$description}\n" .
               " * \n" .
               " * <AUTHOR> .
               " * @version {$version}\n" .
               " * @since {$date}\n" .
               " */\n\n";
    }
    
    /**
     * 生成标准的方法注释
     * 
     * @param string $description 方法描述
     * @param array $params 参数列表
     * @param string $return 返回值类型
     * @param array $throws 异常列表
     * @return string 方法注释
     */
    public static function generateMethodComment($description, $params = [], $return = 'void', $throws = [])
    {
        $comment = ["/**", " * {$description}", " *"];
        
        // 添加参数注释
        foreach ($params as $param) {
            $type = $param['type'] ?? 'mixed';
            $name = $param['name'] ?? '';
            $desc = $param['description'] ?? '';
            $comment[] = " * @param {$type} {$name} {$desc}";
        }
        
        // 添加返回值注释
        if ($return !== 'void') {
            $comment[] = " * @return {$return}";
        }
        
        // 添加异常注释
        foreach ($throws as $exception) {
            $comment[] = " * @throws {$exception}";
        }
        
        $comment[] = " */";
        
        return implode("\n", $comment);
    }
    
    /**
     * 检查代码风格问题
     * 
     * @param string $code PHP代码
     * @return array 问题列表
     */
    public static function checkCodeStyle($code)
    {
        $issues = [];
        $lines = explode("\n", $code);
        
        foreach ($lines as $lineNumber => $line) {
            $lineNum = $lineNumber + 1;
            
            // 检查行尾空格
            if (preg_match('/\s+$/', $line)) {
                $issues[] = "第{$lineNum}行: 行尾有多余空格";
            }
            
            // 检查制表符
            if (strpos($line, "\t") !== false) {
                $issues[] = "第{$lineNum}行: 使用了制表符，建议使用空格";
            }
            
            // 检查行长度
            if (strlen($line) > 120) {
                $issues[] = "第{$lineNum}行: 行长度超过120字符";
            }
            
            // 检查操作符周围的空格
            if (preg_match('/[a-zA-Z0-9][=+\-*\/][a-zA-Z0-9]/', $line)) {
                $issues[] = "第{$lineNum}行: 操作符周围缺少空格";
            }
            
            // 检查逗号后的空格
            if (preg_match('/,[^\s]/', $line)) {
                $issues[] = "第{$lineNum}行: 逗号后缺少空格";
            }
            
            // 检查分号前的空格
            if (preg_match('/\s;/', $line)) {
                $issues[] = "第{$lineNum}行: 分号前有多余空格";
            }
        }
        
        return $issues;
    }
    
    /**
     * 自动修复常见的代码风格问题
     * 
     * @param string $code PHP代码
     * @return string 修复后的代码
     */
    public static function autoFixCodeStyle($code)
    {
        // 移除行尾空格
        $code = preg_replace('/\s+$/m', '', $code);
        
        // 替换制表符为空格
        $code = str_replace("\t", '    ', $code);
        
        // 在操作符周围添加空格
        $code = preg_replace('/([a-zA-Z0-9])([=+\-*\/])([a-zA-Z0-9])/', '$1 $2 $3', $code);
        
        // 在逗号后添加空格
        $code = preg_replace('/,([^\s])/', ', $1', $code);
        
        // 移除分号前的空格
        $code = preg_replace('/\s+;/', ';', $code);
        
        // 标准化大括号位置
        $code = preg_replace('/\)\s*\{/', ') {', $code);
        $code = preg_replace('/\}\s*else\s*\{/', '} else {', $code);
        
        return $code;
    }
    
    /**
     * 转换命名风格
     * 
     * @param string $name 名称
     * @param string $from 源风格 (camel, snake, pascal, kebab)
     * @param string $to 目标风格 (camel, snake, pascal, kebab)
     * @return string 转换后的名称
     */
    public static function convertNamingStyle($name, $from, $to)
    {
        // 先转换为统一的数组格式
        $words = [];
        
        switch ($from) {
            case 'camel':
                $words = preg_split('/(?=[A-Z])/', $name, -1, PREG_SPLIT_NO_EMPTY);
                break;
                
            case 'pascal':
                $words = preg_split('/(?=[A-Z])/', $name, -1, PREG_SPLIT_NO_EMPTY);
                break;
                
            case 'snake':
                $words = explode('_', $name);
                break;
                
            case 'kebab':
                $words = explode('-', $name);
                break;
        }
        
        // 转换为目标格式
        switch ($to) {
            case 'camel':
                $result = strtolower($words[0]);
                for ($i = 1; $i < count($words); $i++) {
                    $result .= ucfirst(strtolower($words[$i]));
                }
                return $result;
                
            case 'pascal':
                $result = '';
                foreach ($words as $word) {
                    $result .= ucfirst(strtolower($word));
                }
                return $result;
                
            case 'snake':
                return strtolower(implode('_', $words));
                
            case 'kebab':
                return strtolower(implode('-', $words));
                
            default:
                return $name;
        }
    }
    
    /**
     * 验证PHP语法
     * 
     * @param string $code PHP代码
     * @return array 验证结果
     */
    public static function validateSyntax($code)
    {
        $result = [
            'valid' => true,
            'errors' => []
        ];
        
        // 创建临时文件
        $tempFile = tempnam(sys_get_temp_dir(), 'php_syntax_check');
        file_put_contents($tempFile, $code);
        
        // 使用php -l检查语法
        $output = [];
        $returnCode = 0;
        exec("php -l {$tempFile} 2>&1", $output, $returnCode);
        
        // 清理临时文件
        unlink($tempFile);
        
        if ($returnCode !== 0) {
            $result['valid'] = false;
            $result['errors'] = $output;
        }
        
        return $result;
    }
}
