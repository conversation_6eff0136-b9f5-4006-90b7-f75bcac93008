<?php
/**
 * API响应助手类
 * 统一API响应格式和错误处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

class ResponseHelper
{
    /**
     * 发送JSON响应
     * 
     * @param mixed $data 响应数据
     * @param int $code HTTP状态码
     * @param string $message 响应消息
     * @param array $extra 额外数据
     * @return void
     */
    public static function json($data, $code = 200, $message = '', $extra = [])
    {
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // 处理OPTIONS请求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
        
        http_response_code($code);
        
        // 构建响应数据
        $response = [
            'code' => $code,
            'success' => $code >= 200 && $code < 300,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'datetime' => date('Y-m-d H:i:s')
        ];
        
        // 合并额外数据
        if (!empty($extra)) {
            $response = array_merge($response, $extra);
        }
        
        // 开发环境添加调试信息
        if (defined('DEBUG') && DEBUG) {
            $response['debug'] = [
                'memory_usage' => memory_get_usage(true),
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
                'request_method' => $_SERVER['REQUEST_METHOD'],
                'request_uri' => $_SERVER['REQUEST_URI']
            ];
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * 发送成功响应
     * 
     * @param mixed $data 响应数据
     * @param string $message 成功消息
     * @param array $extra 额外数据
     * @return void
     */
    public static function success($data = null, $message = '操作成功', $extra = [])
    {
        self::json($data, API_SUCCESS_CODE, $message, $extra);
    }
    
    /**
     * 发送错误响应
     * 
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 错误数据
     * @param array $extra 额外数据
     * @return void
     */
    public static function error($message = '操作失败', $code = null, $data = null, $extra = [])
    {
        if ($code === null) {
            $code = API_ERROR_CODE;
        }
        
        // 记录错误日志
        if ($code >= 500) {
            error_log("API Error [{$code}]: {$message}");
        }
        
        self::json($data, $code, $message, $extra);
    }
    
    /**
     * 发送未授权响应
     * 
     * @param string $message 错误消息
     * @param mixed $data 错误数据
     * @return void
     */
    public static function unauthorized($message = '未授权访问', $data = null)
    {
        self::error($message, API_UNAUTHORIZED_CODE, $data);
    }
    
    /**
     * 发送禁止访问响应
     * 
     * @param string $message 错误消息
     * @param mixed $data 错误数据
     * @return void
     */
    public static function forbidden($message = '禁止访问', $data = null)
    {
        self::error($message, API_FORBIDDEN_CODE, $data);
    }
    
    /**
     * 发送未找到响应
     * 
     * @param string $message 错误消息
     * @param mixed $data 错误数据
     * @return void
     */
    public static function notFound($message = '资源未找到', $data = null)
    {
        self::error($message, API_NOT_FOUND_CODE, $data);
    }
    
    /**
     * 发送服务器错误响应
     * 
     * @param string $message 错误消息
     * @param mixed $data 错误数据
     * @return void
     */
    public static function serverError($message = '服务器内部错误', $data = null)
    {
        self::error($message, API_SERVER_ERROR_CODE, $data);
    }
    
    /**
     * 发送验证失败响应
     * 
     * @param array $errors 验证错误信息
     * @param string $message 错误消息
     * @return void
     */
    public static function validationError($errors, $message = '数据验证失败')
    {
        self::error($message, API_ERROR_CODE, ['validation_errors' => $errors]);
    }
    
    /**
     * 发送分页响应
     * 
     * @param array $data 数据列表
     * @param int $total 总数量
     * @param int $page 当前页码
     * @param int $limit 每页数量
     * @param string $message 响应消息
     * @return void
     */
    public static function paginated($data, $total, $page, $limit, $message = '获取成功')
    {
        $pagination = [
            'current_page' => (int)$page,
            'per_page' => (int)$limit,
            'total' => (int)$total,
            'total_pages' => ceil($total / $limit),
            'has_next' => $page * $limit < $total,
            'has_prev' => $page > 1
        ];
        
        self::success([
            'items' => $data,
            'pagination' => $pagination
        ], $message);
    }
}
