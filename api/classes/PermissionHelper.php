<?php
/**
 * 权限助手类
 * 统一权限检查和管理逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

require_once __DIR__ . '/../config/database.php';

class PermissionHelper
{
    /**
     * 数据库连接
     * @var PDO
     */
    private static $conn = null;
    
    /**
     * 权限缓存
     * @var array
     */
    private static $permissionCache = [];
    
    /**
     * 初始化数据库连接
     * 
     * @return void
     */
    private static function initConnection()
    {
        if (self::$conn === null) {
            $database = Database::getInstance();
            self::$conn = $database->getConnection();
        }
    }
    
    /**
     * 检查用户是否有指定权限
     * 
     * @param int $userId 用户ID
     * @param string $permission 权限名称
     * @param string $module 模块名称
     * @return bool
     */
    public static function hasPermission($userId, $permission, $module = null)
    {
        self::initConnection();
        
        // 检查缓存
        $cacheKey = "{$userId}_{$permission}_{$module}";
        if (isset(self::$permissionCache[$cacheKey])) {
            return self::$permissionCache[$cacheKey];
        }
        
        try {
            // 检查用户是否为超级管理员
            if (self::isSuperAdmin($userId)) {
                self::$permissionCache[$cacheKey] = true;
                return true;
            }
            
            // 构建查询条件
            $sql = "SELECT COUNT(*) FROM user_permissions up 
                    JOIN permissions p ON up.permission_id = p.id 
                    WHERE up.user_id = ? AND p.name = ?";
            $params = [$userId, $permission];
            
            if ($module !== null) {
                $sql .= " AND p.module = ?";
                $params[] = $module;
            }
            
            $sql .= " AND up.status = 'active' AND up.expires_at > NOW()";
            
            $stmt = self::$conn->prepare($sql);
            $stmt->execute($params);
            
            $hasPermission = $stmt->fetchColumn() > 0;
            
            // 缓存结果
            self::$permissionCache[$cacheKey] = $hasPermission;
            
            return $hasPermission;
            
        } catch (Exception $e) {
            error_log("权限检查错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查用户是否为超级管理员
     * 
     * @param int $userId 用户ID
     * @return bool
     */
    public static function isSuperAdmin($userId)
    {
        self::initConnection();
        
        try {
            $sql = "SELECT is_super_admin FROM users WHERE id = ? AND status = 'active'";
            $stmt = self::$conn->prepare($sql);
            $stmt->execute([$userId]);
            
            $result = $stmt->fetchColumn();
            return (bool)$result;
            
        } catch (Exception $e) {
            error_log("超级管理员检查错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取用户所有权限
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public static function getUserPermissions($userId)
    {
        self::initConnection();
        
        try {
            // 如果是超级管理员，返回所有权限
            if (self::isSuperAdmin($userId)) {
                $sql = "SELECT p.*, 'permanent' as expires_at FROM permissions p WHERE p.status = 'active'";
                $stmt = self::$conn->prepare($sql);
                $stmt->execute();
            } else {
                $sql = "SELECT p.*, up.expires_at, up.granted_by, up.granted_at 
                        FROM user_permissions up 
                        JOIN permissions p ON up.permission_id = p.id 
                        WHERE up.user_id = ? AND up.status = 'active' AND up.expires_at > NOW()
                        ORDER BY p.module, p.name";
                $stmt = self::$conn->prepare($sql);
                $stmt->execute([$userId]);
            }
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("获取用户权限错误: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 授予用户权限
     * 
     * @param int $userId 用户ID
     * @param int $permissionId 权限ID
     * @param int $grantedBy 授权者ID
     * @param string $expiresAt 过期时间
     * @return bool
     */
    public static function grantPermission($userId, $permissionId, $grantedBy, $expiresAt = null)
    {
        self::initConnection();
        
        try {
            // 检查权限是否已存在
            $checkSql = "SELECT id FROM user_permissions 
                         WHERE user_id = ? AND permission_id = ? AND status = 'active'";
            $checkStmt = self::$conn->prepare($checkSql);
            $checkStmt->execute([$userId, $permissionId]);
            
            if ($checkStmt->fetchColumn()) {
                // 更新现有权限
                $updateSql = "UPDATE user_permissions 
                              SET expires_at = ?, granted_by = ?, granted_at = NOW() 
                              WHERE user_id = ? AND permission_id = ? AND status = 'active'";
                $updateStmt = self::$conn->prepare($updateSql);
                $result = $updateStmt->execute([$expiresAt, $grantedBy, $userId, $permissionId]);
            } else {
                // 创建新权限
                $insertSql = "INSERT INTO user_permissions 
                              (user_id, permission_id, granted_by, granted_at, expires_at, status) 
                              VALUES (?, ?, ?, NOW(), ?, 'active')";
                $insertStmt = self::$conn->prepare($insertSql);
                $result = $insertStmt->execute([$userId, $permissionId, $grantedBy, $expiresAt]);
            }
            
            // 清除缓存
            self::clearUserPermissionCache($userId);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("授予权限错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 撤销用户权限
     * 
     * @param int $userId 用户ID
     * @param int $permissionId 权限ID
     * @param int $revokedBy 撤销者ID
     * @return bool
     */
    public static function revokePermission($userId, $permissionId, $revokedBy)
    {
        self::initConnection();
        
        try {
            $sql = "UPDATE user_permissions 
                    SET status = 'revoked', revoked_by = ?, revoked_at = NOW() 
                    WHERE user_id = ? AND permission_id = ? AND status = 'active'";
            $stmt = self::$conn->prepare($sql);
            $result = $stmt->execute([$revokedBy, $userId, $permissionId]);
            
            // 清除缓存
            self::clearUserPermissionCache($userId);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("撤销权限错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查权限是否存在
     * 
     * @param string $permission 权限名称
     * @param string $module 模块名称
     * @return bool
     */
    public static function permissionExists($permission, $module = null)
    {
        self::initConnection();
        
        try {
            $sql = "SELECT COUNT(*) FROM permissions WHERE name = ?";
            $params = [$permission];
            
            if ($module !== null) {
                $sql .= " AND module = ?";
                $params[] = $module;
            }
            
            $stmt = self::$conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log("权限存在检查错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取所有可用权限
     * 
     * @param string $module 模块名称（可选）
     * @return array
     */
    public static function getAvailablePermissions($module = null)
    {
        self::initConnection();
        
        try {
            $sql = "SELECT * FROM permissions WHERE status = 'active'";
            $params = [];
            
            if ($module !== null) {
                $sql .= " AND module = ?";
                $params[] = $module;
            }
            
            $sql .= " ORDER BY module, name";
            
            $stmt = self::$conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("获取可用权限错误: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 批量授予权限
     * 
     * @param int $userId 用户ID
     * @param array $permissionIds 权限ID数组
     * @param int $grantedBy 授权者ID
     * @param string $expiresAt 过期时间
     * @return bool
     */
    public static function batchGrantPermissions($userId, $permissionIds, $grantedBy, $expiresAt = null)
    {
        self::initConnection();
        
        try {
            self::$conn->beginTransaction();
            
            foreach ($permissionIds as $permissionId) {
                if (!self::grantPermission($userId, $permissionId, $grantedBy, $expiresAt)) {
                    throw new Exception("授予权限失败: {$permissionId}");
                }
            }
            
            self::$conn->commit();
            return true;
            
        } catch (Exception $e) {
            self::$conn->rollback();
            error_log("批量授予权限错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量撤销权限
     * 
     * @param int $userId 用户ID
     * @param array $permissionIds 权限ID数组
     * @param int $revokedBy 撤销者ID
     * @return bool
     */
    public static function batchRevokePermissions($userId, $permissionIds, $revokedBy)
    {
        self::initConnection();
        
        try {
            self::$conn->beginTransaction();
            
            foreach ($permissionIds as $permissionId) {
                if (!self::revokePermission($userId, $permissionId, $revokedBy)) {
                    throw new Exception("撤销权限失败: {$permissionId}");
                }
            }
            
            self::$conn->commit();
            return true;
            
        } catch (Exception $e) {
            self::$conn->rollback();
            error_log("批量撤销权限错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 清除用户权限缓存
     * 
     * @param int $userId 用户ID
     * @return void
     */
    public static function clearUserPermissionCache($userId)
    {
        foreach (self::$permissionCache as $key => $value) {
            if (strpos($key, "{$userId}_") === 0) {
                unset(self::$permissionCache[$key]);
            }
        }
    }
    
    /**
     * 清除所有权限缓存
     * 
     * @return void
     */
    public static function clearAllPermissionCache()
    {
        self::$permissionCache = [];
    }
    
    /**
     * 检查权限是否即将过期
     * 
     * @param int $userId 用户ID
     * @param int $days 天数阈值
     * @return array 即将过期的权限
     */
    public static function getExpiringPermissions($userId, $days = 7)
    {
        self::initConnection();
        
        try {
            $sql = "SELECT p.*, up.expires_at 
                    FROM user_permissions up 
                    JOIN permissions p ON up.permission_id = p.id 
                    WHERE up.user_id = ? AND up.status = 'active' 
                    AND up.expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY)
                    ORDER BY up.expires_at";
            
            $stmt = self::$conn->prepare($sql);
            $stmt->execute([$userId, $days]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("获取即将过期权限错误: " . $e->getMessage());
            return [];
        }
    }
}
