<?php
/**
 * 系统监控类
 * 提供系统性能监控和健康检查功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

require_once __DIR__ . '/Logger.php';

class Monitor
{
    /**
     * 监控数据存储
     * @var array
     */
    private static $metrics = [];
    
    /**
     * 性能计时器
     * @var array
     */
    private static $timers = [];
    
    /**
     * 内存使用记录
     * @var array
     */
    private static $memoryUsage = [];
    
    /**
     * 开始性能计时
     * 
     * @param string $name 计时器名称
     * @return void
     */
    public static function startTimer($name)
    {
        self::$timers[$name] = [
            'start' => microtime(true),
            'memory_start' => memory_get_usage(true)
        ];
    }
    
    /**
     * 结束性能计时
     * 
     * @param string $name 计时器名称
     * @return array 性能数据
     */
    public static function endTimer($name)
    {
        if (!isset(self::$timers[$name])) {
            return null;
        }
        
        $timer = self::$timers[$name];
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $result = [
            'name' => $name,
            'duration' => round(($endTime - $timer['start']) * 1000, 2), // 毫秒
            'memory_used' => $endMemory - $timer['memory_start'],
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 记录到监控数据
        self::$metrics['performance'][] = $result;
        
        // 清理计时器
        unset(self::$timers[$name]);
        
        // 记录日志
        Logger::debug("性能监控: {$name}", $result);
        
        return $result;
    }
    
    /**
     * 记录自定义指标
     * 
     * @param string $name 指标名称
     * @param mixed $value 指标值
     * @param array $tags 标签
     * @return void
     */
    public static function recordMetric($name, $value, array $tags = [])
    {
        $metric = [
            'name' => $name,
            'value' => $value,
            'tags' => $tags,
            'timestamp' => time()
        ];
        
        self::$metrics['custom'][] = $metric;
        
        Logger::info("自定义指标: {$name} = {$value}", $metric);
    }
    
    /**
     * 获取系统信息
     * 
     * @return array 系统信息
     */
    public static function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'timezone' => date_default_timezone_get(),
            'server_time' => date('Y-m-d H:i:s'),
            'uptime' => self::getServerUptime()
        ];
    }
    
    /**
     * 获取内存使用情况
     * 
     * @return array 内存使用信息
     */
    public static function getMemoryUsage()
    {
        return [
            'current_usage' => memory_get_usage(true),
            'current_usage_formatted' => self::formatBytes(memory_get_usage(true)),
            'peak_usage' => memory_get_peak_usage(true),
            'peak_usage_formatted' => self::formatBytes(memory_get_peak_usage(true)),
            'limit' => self::parseMemoryLimit(ini_get('memory_limit')),
            'limit_formatted' => ini_get('memory_limit'),
            'usage_percentage' => round((memory_get_usage(true) / self::parseMemoryLimit(ini_get('memory_limit'))) * 100, 2)
        ];
    }
    
    /**
     * 获取数据库连接状态
     * 
     * @return array 数据库状态
     */
    public static function getDatabaseStatus()
    {
        try {
            $database = Database::getInstance();
            $conn = $database->getConnection();
            
            // 测试连接
            $stmt = $conn->query('SELECT 1');
            $isConnected = $stmt !== false;
            
            // 获取数据库信息
            $info = $database->getDatabaseInfo();
            
            return [
                'connected' => $isConnected,
                'server_version' => $info['server_version'] ?? 'Unknown',
                'connection_status' => $info['connection_status'] ?? 'Unknown',
                'current_connections' => $info['current_connections'] ?? 0,
                'pool_size' => $info['pool_size'] ?? 0,
                'transaction_level' => $info['transaction_level'] ?? 0
            ];
            
        } catch (Exception $e) {
            Logger::error('数据库状态检查失败', ['error' => $e->getMessage()]);
            return [
                'connected' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查磁盘空间
     * 
     * @return array 磁盘空间信息
     */
    public static function getDiskUsage()
    {
        $path = __DIR__ . '/../../';
        
        return [
            'total_space' => disk_total_space($path),
            'total_space_formatted' => self::formatBytes(disk_total_space($path)),
            'free_space' => disk_free_space($path),
            'free_space_formatted' => self::formatBytes(disk_free_space($path)),
            'used_space' => disk_total_space($path) - disk_free_space($path),
            'used_space_formatted' => self::formatBytes(disk_total_space($path) - disk_free_space($path)),
            'usage_percentage' => round(((disk_total_space($path) - disk_free_space($path)) / disk_total_space($path)) * 100, 2)
        ];
    }
    
    /**
     * 获取服务器负载
     * 
     * @return array|null 服务器负载信息
     */
    public static function getServerLoad()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1_minute' => $load[0],
                '5_minutes' => $load[1],
                '15_minutes' => $load[2]
            ];
        }
        
        return null;
    }
    
    /**
     * 健康检查
     * 
     * @return array 健康检查结果
     */
    public static function healthCheck()
    {
        $checks = [];
        $overallStatus = 'healthy';
        
        // 数据库检查
        $dbStatus = self::getDatabaseStatus();
        $checks['database'] = [
            'status' => $dbStatus['connected'] ? 'healthy' : 'unhealthy',
            'details' => $dbStatus
        ];
        
        if (!$dbStatus['connected']) {
            $overallStatus = 'unhealthy';
        }
        
        // 内存检查
        $memoryUsage = self::getMemoryUsage();
        $memoryStatus = 'healthy';
        if ($memoryUsage['usage_percentage'] > 90) {
            $memoryStatus = 'critical';
            $overallStatus = 'unhealthy';
        } elseif ($memoryUsage['usage_percentage'] > 80) {
            $memoryStatus = 'warning';
            if ($overallStatus === 'healthy') {
                $overallStatus = 'warning';
            }
        }
        
        $checks['memory'] = [
            'status' => $memoryStatus,
            'details' => $memoryUsage
        ];
        
        // 磁盘空间检查
        $diskUsage = self::getDiskUsage();
        $diskStatus = 'healthy';
        if ($diskUsage['usage_percentage'] > 95) {
            $diskStatus = 'critical';
            $overallStatus = 'unhealthy';
        } elseif ($diskUsage['usage_percentage'] > 85) {
            $diskStatus = 'warning';
            if ($overallStatus === 'healthy') {
                $overallStatus = 'warning';
            }
        }
        
        $checks['disk'] = [
            'status' => $diskStatus,
            'details' => $diskUsage
        ];
        
        // 日志目录检查
        $logPath = Logger::getConfig('log_path');
        $logStatus = is_writable($logPath) ? 'healthy' : 'unhealthy';
        if ($logStatus === 'unhealthy') {
            $overallStatus = 'unhealthy';
        }
        
        $checks['logs'] = [
            'status' => $logStatus,
            'details' => [
                'path' => $logPath,
                'writable' => is_writable($logPath),
                'exists' => is_dir($logPath)
            ]
        ];
        
        return [
            'overall_status' => $overallStatus,
            'timestamp' => date('Y-m-d H:i:s'),
            'checks' => $checks
        ];
    }
    
    /**
     * 获取所有监控指标
     * 
     * @return array 监控指标
     */
    public static function getAllMetrics()
    {
        return [
            'system_info' => self::getSystemInfo(),
            'memory_usage' => self::getMemoryUsage(),
            'database_status' => self::getDatabaseStatus(),
            'disk_usage' => self::getDiskUsage(),
            'server_load' => self::getServerLoad(),
            'health_check' => self::healthCheck(),
            'custom_metrics' => self::$metrics
        ];
    }
    
    /**
     * 清理监控数据
     * 
     * @return void
     */
    public static function clearMetrics()
    {
        self::$metrics = [];
        self::$timers = [];
        self::$memoryUsage = [];
    }
    
    /**
     * 格式化字节数
     * 
     * @param int $bytes 字节数
     * @return string 格式化后的字符串
     */
    private static function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 解析内存限制
     * 
     * @param string $memoryLimit 内存限制字符串
     * @return int 字节数
     */
    private static function parseMemoryLimit($memoryLimit)
    {
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int)$memoryLimit;
        
        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
    
    /**
     * 获取服务器运行时间
     * 
     * @return string|null 运行时间
     */
    private static function getServerUptime()
    {
        if (PHP_OS_FAMILY === 'Linux') {
            $uptime = file_get_contents('/proc/uptime');
            if ($uptime) {
                $seconds = (int)explode(' ', $uptime)[0];
                return self::formatUptime($seconds);
            }
        }
        
        return null;
    }
    
    /**
     * 格式化运行时间
     * 
     * @param int $seconds 秒数
     * @return string 格式化后的时间
     */
    private static function formatUptime($seconds)
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        
        return "{$days}天 {$hours}小时 {$minutes}分钟";
    }
}
