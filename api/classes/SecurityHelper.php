<?php
/**
 * 安全助手类
 * 统一安全性检查、输入验证、权限检查等逻辑
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

class SecurityHelper
{
    /**
     * XSS过滤
     * 
     * @param mixed $data 待过滤数据
     * @return mixed 过滤后的数据
     */
    public static function filterXSS($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'filterXSS'], $data);
        }
        
        if (!is_string($data)) {
            return $data;
        }
        
        // 移除危险标签
        $data = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $data);
        $data = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $data);
        $data = preg_replace('/javascript:/i', '', $data);
        $data = preg_replace('/on\w+\s*=/i', '', $data);
        
        // HTML实体编码
        return htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
    
    /**
     * SQL注入防护
     * 
     * @param string $data 待检查数据
     * @return bool 是否包含SQL注入风险
     */
    public static function detectSQLInjection($data)
    {
        if (!is_string($data)) {
            return false;
        }
        
        $patterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\btruncate\b.*\btable\b)/i',
            '/(\bexec\b|\bexecute\b)/i',
            '/(\'|\"|;|--|\#|\*|\/\*|\*\/)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $data)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 验证CSRF令牌
     * 
     * @param string $token 提交的令牌
     * @param string $sessionToken 会话中的令牌
     * @return bool
     */
    public static function validateCSRFToken($token, $sessionToken)
    {
        return hash_equals($sessionToken, $token);
    }
    
    /**
     * 生成CSRF令牌
     * 
     * @return string
     */
    public static function generateCSRFToken()
    {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * 验证IP地址格式
     * 
     * @param string $ip IP地址
     * @return bool
     */
    public static function validateIP($ip)
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * 检查IP是否在白名单中
     * 
     * @param string $ip IP地址
     * @param array $whitelist 白名单
     * @return bool
     */
    public static function isIPWhitelisted($ip, $whitelist = [])
    {
        if (empty($whitelist)) {
            return true; // 如果没有白名单，允许所有IP
        }
        
        foreach ($whitelist as $allowedIP) {
            if (self::matchIPPattern($ip, $allowedIP)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查IP是否在黑名单中
     * 
     * @param string $ip IP地址
     * @param array $blacklist 黑名单
     * @return bool
     */
    public static function isIPBlacklisted($ip, $blacklist = [])
    {
        foreach ($blacklist as $blockedIP) {
            if (self::matchIPPattern($ip, $blockedIP)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * IP模式匹配（支持CIDR和通配符）
     * 
     * @param string $ip 待检查的IP
     * @param string $pattern IP模式
     * @return bool
     */
    private static function matchIPPattern($ip, $pattern)
    {
        // 精确匹配
        if ($ip === $pattern) {
            return true;
        }
        
        // CIDR匹配
        if (strpos($pattern, '/') !== false) {
            list($subnet, $mask) = explode('/', $pattern);
            $ipLong = ip2long($ip);
            $subnetLong = ip2long($subnet);
            $maskLong = -1 << (32 - (int)$mask);
            
            return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
        }
        
        // 通配符匹配
        if (strpos($pattern, '*') !== false) {
            $pattern = str_replace('*', '.*', preg_quote($pattern, '/'));
            return preg_match("/^{$pattern}$/", $ip);
        }
        
        return false;
    }
    
    /**
     * 验证用户代理字符串
     * 
     * @param string $userAgent 用户代理字符串
     * @return bool
     */
    public static function validateUserAgent($userAgent)
    {
        if (empty($userAgent) || strlen($userAgent) > 500) {
            return false;
        }
        
        // 检查是否包含恶意模式
        $maliciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 频率限制检查
     * 
     * @param string $key 限制键（通常是IP或用户ID）
     * @param int $maxRequests 最大请求数
     * @param int $timeWindow 时间窗口（秒）
     * @return bool 是否超过限制
     */
    public static function checkRateLimit($key, $maxRequests = 60, $timeWindow = 60)
    {
        $cacheKey = "rate_limit_{$key}";
        $currentTime = time();
        
        // 这里应该使用Redis或其他缓存系统，暂时使用文件缓存
        $cacheFile = sys_get_temp_dir() . "/rate_limit_{$key}.cache";
        
        $requests = [];
        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            $requests = json_decode($data, true) ?: [];
        }
        
        // 清理过期的请求记录
        $requests = array_filter($requests, function($timestamp) use ($currentTime, $timeWindow) {
            return ($currentTime - $timestamp) < $timeWindow;
        });
        
        // 检查是否超过限制
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        // 记录当前请求
        $requests[] = $currentTime;
        file_put_contents($cacheFile, json_encode($requests));
        
        return true;
    }
    
    /**
     * 密码强度检查
     * 
     * @param string $password 密码
     * @return array 检查结果
     */
    public static function checkPasswordStrength($password)
    {
        $result = [
            'score' => 0,
            'strength' => 'weak',
            'suggestions' => []
        ];
        
        $length = strlen($password);
        
        // 长度检查
        if ($length >= 8) {
            $result['score'] += 1;
        } else {
            $result['suggestions'][] = '密码长度至少8位';
        }
        
        if ($length >= 12) {
            $result['score'] += 1;
        }
        
        // 包含小写字母
        if (preg_match('/[a-z]/', $password)) {
            $result['score'] += 1;
        } else {
            $result['suggestions'][] = '包含小写字母';
        }
        
        // 包含大写字母
        if (preg_match('/[A-Z]/', $password)) {
            $result['score'] += 1;
        } else {
            $result['suggestions'][] = '包含大写字母';
        }
        
        // 包含数字
        if (preg_match('/[0-9]/', $password)) {
            $result['score'] += 1;
        } else {
            $result['suggestions'][] = '包含数字';
        }
        
        // 包含特殊字符
        if (preg_match('/[^a-zA-Z0-9]/', $password)) {
            $result['score'] += 1;
        } else {
            $result['suggestions'][] = '包含特殊字符';
        }
        
        // 不包含常见弱密码
        $commonPasswords = ['123456', 'password', '123456789', '12345678', 'qwerty'];
        if (!in_array(strtolower($password), $commonPasswords)) {
            $result['score'] += 1;
        } else {
            $result['suggestions'][] = '避免使用常见密码';
        }
        
        // 计算强度等级
        if ($result['score'] >= 6) {
            $result['strength'] = 'strong';
        } elseif ($result['score'] >= 4) {
            $result['strength'] = 'medium';
        }
        
        return $result;
    }
    
    /**
     * 生成安全的随机字符串
     * 
     * @param int $length 长度
     * @param string $chars 字符集
     * @return string
     */
    public static function generateSecureRandomString($length = 32, $chars = null)
    {
        if ($chars === null) {
            $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        }
        
        $result = '';
        $charsLength = strlen($chars);
        
        for ($i = 0; $i < $length; $i++) {
            $result .= $chars[random_int(0, $charsLength - 1)];
        }
        
        return $result;
    }
    
    /**
     * 安全的文件上传检查
     * 
     * @param array $file $_FILES数组中的文件信息
     * @param array $allowedTypes 允许的文件类型
     * @param int $maxSize 最大文件大小（字节）
     * @return array 检查结果
     */
    public static function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880)
    {
        $result = [
            'valid' => false,
            'errors' => []
        ];
        
        // 检查文件是否上传成功
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['errors'][] = '文件上传失败';
            return $result;
        }
        
        // 检查文件大小
        if ($file['size'] > $maxSize) {
            $result['errors'][] = '文件大小超过限制';
            return $result;
        }
        
        // 检查文件类型
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!empty($allowedTypes) && !in_array($fileExtension, $allowedTypes)) {
            $result['errors'][] = '不支持的文件类型';
            return $result;
        }
        
        // 检查MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf',
            'txt' => 'text/plain'
        ];
        
        if (isset($allowedMimes[$fileExtension]) && $mimeType !== $allowedMimes[$fileExtension]) {
            $result['errors'][] = '文件类型与扩展名不匹配';
            return $result;
        }
        
        $result['valid'] = true;
        return $result;
    }
}
