<?php
/**
 * 基础控制器类
 * 提供通用的控制器功能和方法
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

abstract class BaseController
{
    /**
     * 数据库连接实例
     * @var PDO
     */
    protected $conn;
    
    /**
     * 数据库实例
     * @var Database
     */
    protected $database;
    
    /**
     * 当前用户信息
     * @var array|null
     */
    protected $currentUser = null;
    
    /**
     * 允许的HTTP方法
     * @var array
     */
    protected $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
    
    /**
     * 是否需要认证
     * @var bool
     */
    protected $requireAuth = true;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->initializeDatabase();
        $this->handleCors();
        $this->validateHttpMethod();
        
        if ($this->requireAuth) {
            $this->authenticateUser();
        }
    }
    
    /**
     * 初始化数据库连接
     * 
     * @return void
     */
    protected function initializeDatabase()
    {
        try {
            $this->database = new Database();
            $this->conn = $this->database->getConnection();
        } catch (Exception $e) {
            ResponseHelper::serverError('数据库连接失败');
        }
    }
    
    /**
     * 处理跨域请求
     * 
     * @return void
     */
    protected function handleCors()
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: ' . implode(', ', $this->allowedMethods));
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
    
    /**
     * 验证HTTP方法
     * 
     * @return void
     */
    protected function validateHttpMethod()
    {
        if (!in_array($_SERVER['REQUEST_METHOD'], $this->allowedMethods)) {
            ResponseHelper::error('不支持的HTTP方法', 405);
        }
    }
    
    /**
     * 用户认证
     * 
     * @return void
     */
    protected function authenticateUser()
    {
        try {
            $token = $this->getAuthToken();
            if (!$token) {
                ResponseHelper::unauthorized('缺少认证令牌');
            }
            
            // 验证JWT token
            require_once __DIR__ . '/../../includes/jwt_helper.php';
            $jwtHelper = new JWTHelper();
            $payload = $jwtHelper->verifyToken($token);
            
            if (!$payload) {
                ResponseHelper::unauthorized('无效的认证令牌');
            }
            
            // 获取用户信息
            $this->currentUser = $this->getUserById($payload['user_id']);
            if (!$this->currentUser) {
                ResponseHelper::unauthorized('用户不存在或已被禁用');
            }
            
        } catch (Exception $e) {
            ResponseHelper::unauthorized($e->getMessage());
        }
    }
    
    /**
     * 获取认证令牌
     * 
     * @return string|null
     */
    protected function getAuthToken()
    {
        $headers = getallheaders();
        
        // 从Authorization头获取
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        // 从GET参数获取
        if (isset($_GET['token'])) {
            return $_GET['token'];
        }
        
        // 从POST参数获取
        if (isset($_POST['token'])) {
            return $_POST['token'];
        }
        
        return null;
    }
    
    /**
     * 根据ID获取用户信息
     * 
     * @param int $userId 用户ID
     * @return array|null
     */
    protected function getUserById($userId)
    {
        try {
            $sql = "SELECT id, username, email, real_name, status, created_at, last_login 
                    FROM users WHERE id = ? AND status = 'active'";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 获取请求数据
     * 
     * @param bool $assoc 是否返回关联数组
     * @return array|object
     */
    protected function getRequestData($assoc = true)
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, $assoc);
        
        // 如果JSON解析失败，尝试从POST获取
        if (json_last_error() !== JSON_ERROR_NONE) {
            $data = $_POST;
        }
        
        return $data ?: [];
    }
    
    /**
     * 验证请求数据
     * 
     * @param array $rules 验证规则
     * @param array $messages 自定义错误消息
     * @return array 验证通过的数据
     */
    protected function validateRequest($rules, $messages = [])
    {
        $data = $this->getRequestData();
        return ValidationHelper::validateOrFail($data, $rules, $messages);
    }
    
    /**
     * 记录操作日志
     * 
     * @param string $action 操作类型
     * @param string $description 操作描述
     * @param array $data 相关数据
     * @return void
     */
    protected function logOperation($action, $description, $data = [])
    {
        try {
            $sql = "INSERT INTO operation_logs (user_id, action, description, ip_address, user_agent, data, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $this->currentUser['id'] ?? null,
                $action,
                $description,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode($data, JSON_UNESCAPED_UNICODE)
            ]);
        } catch (Exception $e) {
            // 日志记录失败不影响主要功能
            error_log('操作日志记录失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @return string
     */
    protected function getClientIP()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 检查权限
     * 
     * @param string $permission 权限名称
     * @return bool
     */
    protected function hasPermission($permission)
    {
        if (!$this->currentUser) {
            return false;
        }
        
        // 这里可以根据实际的权限系统进行实现
        // 暂时返回true，具体实现可以在子类中重写
        return true;
    }
    
    /**
     * 要求特定权限
     * 
     * @param string $permission 权限名称
     * @return void
     */
    protected function requirePermission($permission)
    {
        if (!$this->hasPermission($permission)) {
            ResponseHelper::forbidden('权限不足');
        }
    }
    
    /**
     * 开始数据库事务
     * 
     * @return void
     */
    protected function beginTransaction()
    {
        $this->conn->beginTransaction();
    }
    
    /**
     * 提交数据库事务
     * 
     * @return void
     */
    protected function commit()
    {
        $this->conn->commit();
    }
    
    /**
     * 回滚数据库事务
     * 
     * @return void
     */
    protected function rollback()
    {
        $this->conn->rollback();
    }
}
