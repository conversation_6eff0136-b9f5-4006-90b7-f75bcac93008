<?php
/**
 * 代码分析工具类
 * 用于分析代码质量、查找未使用代码、优化建议等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

class CodeAnalyzer
{
    /**
     * 项目根目录
     * @var string
     */
    private $projectRoot;
    
    /**
     * 扫描的文件扩展名
     * @var array
     */
    private $extensions = ['php', 'js', 'css'];
    
    /**
     * 排除的目录
     * @var array
     */
    private $excludeDirs = ['vendor', 'node_modules', '.git', 'logs', 'uploads', 'backups'];
    
    /**
     * 分析结果
     * @var array
     */
    private $analysisResult = [];
    
    /**
     * 构造函数
     * 
     * @param string $projectRoot 项目根目录
     */
    public function __construct($projectRoot = null)
    {
        $this->projectRoot = $projectRoot ?: __DIR__ . '/../../';
    }
    
    /**
     * 执行完整的代码分析
     * 
     * @return array 分析结果
     */
    public function analyze()
    {
        $this->analysisResult = [
            'summary' => [],
            'unused_files' => [],
            'unused_functions' => [],
            'unused_variables' => [],
            'duplicate_code' => [],
            'performance_issues' => [],
            'security_issues' => [],
            'code_quality' => []
        ];
        
        $files = $this->scanFiles();
        
        $this->analyzeFileUsage($files);
        $this->analyzeFunctionUsage($files);
        $this->analyzeVariableUsage($files);
        $this->analyzeDuplicateCode($files);
        $this->analyzePerformanceIssues($files);
        $this->analyzeSecurityIssues($files);
        $this->analyzeCodeQuality($files);
        
        $this->generateSummary();
        
        return $this->analysisResult;
    }
    
    /**
     * 扫描项目文件
     * 
     * @return array 文件列表
     */
    private function scanFiles()
    {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->projectRoot)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = str_replace($this->projectRoot, '', $file->getPathname());
                
                // 跳过排除的目录
                $skip = false;
                foreach ($this->excludeDirs as $excludeDir) {
                    if (strpos($relativePath, $excludeDir) === 0) {
                        $skip = true;
                        break;
                    }
                }
                
                if ($skip) {
                    continue;
                }
                
                $extension = pathinfo($file->getFilename(), PATHINFO_EXTENSION);
                if (in_array($extension, $this->extensions)) {
                    $files[] = [
                        'path' => $file->getPathname(),
                        'relative_path' => $relativePath,
                        'extension' => $extension,
                        'size' => $file->getSize(),
                        'modified' => $file->getMTime()
                    ];
                }
            }
        }
        
        return $files;
    }
    
    /**
     * 分析文件使用情况
     * 
     * @param array $files 文件列表
     * @return void
     */
    private function analyzeFileUsage($files)
    {
        $phpFiles = array_filter($files, function($file) {
            return $file['extension'] === 'php';
        });
        
        $allContent = '';
        foreach ($phpFiles as $file) {
            $allContent .= file_get_contents($file['path']) . "\n";
        }
        
        foreach ($phpFiles as $file) {
            $filename = basename($file['path']);
            $relativePath = $file['relative_path'];
            
            // 检查文件是否被引用
            $isReferenced = false;
            
            // 检查require/include语句
            if (preg_match('/(?:require|include)(?:_once)?\s*[\'"].*' . preg_quote($filename, '/') . '[\'"]/', $allContent)) {
                $isReferenced = true;
            }
            
            // 检查类名引用（假设文件名与类名相同）
            $className = pathinfo($filename, PATHINFO_FILENAME);
            if (preg_match('/\bnew\s+' . preg_quote($className, '/') . '\b/', $allContent) ||
                preg_match('/\b' . preg_quote($className, '/') . '::\w+/', $allContent)) {
                $isReferenced = true;
            }
            
            // 排除入口文件和配置文件
            $entryFiles = ['index.php', 'config.php', 'database.php'];
            if (in_array($filename, $entryFiles)) {
                $isReferenced = true;
            }
            
            if (!$isReferenced) {
                $this->analysisResult['unused_files'][] = [
                    'path' => $relativePath,
                    'size' => $file['size'],
                    'last_modified' => date('Y-m-d H:i:s', $file['modified'])
                ];
            }
        }
    }
    
    /**
     * 分析函数使用情况
     * 
     * @param array $files 文件列表
     * @return void
     */
    private function analyzeFunctionUsage($files)
    {
        $phpFiles = array_filter($files, function($file) {
            return $file['extension'] === 'php';
        });
        
        $definedFunctions = [];
        $usedFunctions = [];
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file['path']);
            
            // 查找函数定义
            preg_match_all('/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $content, $matches);
            foreach ($matches[1] as $functionName) {
                $definedFunctions[$functionName] = $file['relative_path'];
            }
            
            // 查找函数调用
            preg_match_all('/([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $content, $matches);
            foreach ($matches[1] as $functionName) {
                if (!in_array($functionName, ['if', 'for', 'while', 'foreach', 'switch', 'function', 'class'])) {
                    $usedFunctions[] = $functionName;
                }
            }
        }
        
        foreach ($definedFunctions as $functionName => $filePath) {
            if (!in_array($functionName, $usedFunctions)) {
                $this->analysisResult['unused_functions'][] = [
                    'function' => $functionName,
                    'file' => $filePath
                ];
            }
        }
    }
    
    /**
     * 分析变量使用情况
     * 
     * @param array $files 文件列表
     * @return void
     */
    private function analyzeVariableUsage($files)
    {
        $phpFiles = array_filter($files, function($file) {
            return $file['extension'] === 'php';
        });
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file['path']);
            $lines = explode("\n", $content);
            
            foreach ($lines as $lineNumber => $line) {
                // 查找变量定义但未使用的情况
                if (preg_match('/\$([a-zA-Z_][a-zA-Z0-9_]*)\s*=/', $line, $matches)) {
                    $variableName = $matches[1];
                    
                    // 检查变量是否在后续代码中使用
                    $remainingContent = implode("\n", array_slice($lines, $lineNumber + 1));
                    if (!preg_match('/\$' . preg_quote($variableName, '/') . '\b/', $remainingContent)) {
                        // 排除一些常见的变量
                        if (!in_array($variableName, ['_GET', '_POST', '_SESSION', '_COOKIE', '_SERVER', '_FILES'])) {
                            $this->analysisResult['unused_variables'][] = [
                                'variable' => '$' . $variableName,
                                'file' => $file['relative_path'],
                                'line' => $lineNumber + 1
                            ];
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 分析重复代码
     * 
     * @param array $files 文件列表
     * @return void
     */
    private function analyzeDuplicateCode($files)
    {
        $phpFiles = array_filter($files, function($file) {
            return $file['extension'] === 'php';
        });
        
        $codeBlocks = [];
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file['path']);
            $lines = explode("\n", $content);
            
            // 分析5行以上的代码块
            for ($i = 0; $i < count($lines) - 5; $i++) {
                $block = array_slice($lines, $i, 5);
                $blockContent = trim(implode("\n", $block));
                
                if (strlen($blockContent) > 100) { // 只分析较长的代码块
                    $hash = md5($blockContent);
                    
                    if (!isset($codeBlocks[$hash])) {
                        $codeBlocks[$hash] = [];
                    }
                    
                    $codeBlocks[$hash][] = [
                        'file' => $file['relative_path'],
                        'start_line' => $i + 1,
                        'content' => $blockContent
                    ];
                }
            }
        }
        
        foreach ($codeBlocks as $hash => $blocks) {
            if (count($blocks) > 1) {
                $this->analysisResult['duplicate_code'][] = [
                    'hash' => $hash,
                    'occurrences' => count($blocks),
                    'locations' => $blocks
                ];
            }
        }
    }
    
    /**
     * 分析性能问题
     * 
     * @param array $files 文件列表
     * @return void
     */
    private function analyzePerformanceIssues($files)
    {
        $phpFiles = array_filter($files, function($file) {
            return $file['extension'] === 'php';
        });
        
        $performancePatterns = [
            'nested_loops' => '/for\s*\([^}]*for\s*\(/s',
            'sql_in_loop' => '/(?:for|while|foreach)[^}]*(?:query|prepare|execute)/s',
            'file_operations_in_loop' => '/(?:for|while|foreach)[^}]*(?:file_get_contents|fopen|fwrite)/s',
            'inefficient_array_search' => '/in_array\s*\([^,]+,\s*\$[a-zA-Z_][a-zA-Z0-9_]*\s*\)/',
            'multiple_db_connections' => '/new\s+(?:PDO|mysqli|Database)\s*\(/g'
        ];
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file['path']);
            
            foreach ($performancePatterns as $issue => $pattern) {
                if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                    foreach ($matches[0] as $match) {
                        $lineNumber = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                        
                        $this->analysisResult['performance_issues'][] = [
                            'type' => $issue,
                            'file' => $file['relative_path'],
                            'line' => $lineNumber,
                            'code' => trim($match[0])
                        ];
                    }
                }
            }
        }
    }
    
    /**
     * 分析安全问题
     * 
     * @param array $files 文件列表
     * @return void
     */
    private function analyzeSecurityIssues($files)
    {
        $phpFiles = array_filter($files, function($file) {
            return $file['extension'] === 'php';
        });
        
        $securityPatterns = [
            'sql_injection' => '/\$_(?:GET|POST|REQUEST)\[[^]]+\][^;]*(?:query|prepare|execute)/',
            'xss_vulnerability' => '/echo\s+\$_(?:GET|POST|REQUEST)/',
            'file_inclusion' => '/(?:include|require)(?:_once)?\s*\(\s*\$_(?:GET|POST|REQUEST)/',
            'eval_usage' => '/eval\s*\(/',
            'exec_usage' => '/(?:exec|system|shell_exec|passthru)\s*\(/',
            'unfiltered_input' => '/\$_(?:GET|POST|REQUEST)\[[^]]+\]\s*(?:;|\)|\]|,)/'
        ];
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file['path']);
            
            foreach ($securityPatterns as $issue => $pattern) {
                if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                    foreach ($matches[0] as $match) {
                        $lineNumber = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                        
                        $this->analysisResult['security_issues'][] = [
                            'type' => $issue,
                            'file' => $file['relative_path'],
                            'line' => $lineNumber,
                            'code' => trim($match[0])
                        ];
                    }
                }
            }
        }
    }
    
    /**
     * 分析代码质量
     * 
     * @param array $files 文件列表
     * @return void
     */
    private function analyzeCodeQuality($files)
    {
        $phpFiles = array_filter($files, function($file) {
            return $file['extension'] === 'php';
        });
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file['path']);
            $lines = explode("\n", $content);
            
            $issues = [];
            
            foreach ($lines as $lineNumber => $line) {
                // 检查行长度
                if (strlen($line) > 120) {
                    $issues[] = [
                        'type' => 'long_line',
                        'line' => $lineNumber + 1,
                        'message' => '行长度超过120字符'
                    ];
                }
                
                // 检查制表符
                if (strpos($line, "\t") !== false) {
                    $issues[] = [
                        'type' => 'tab_usage',
                        'line' => $lineNumber + 1,
                        'message' => '使用了制表符，建议使用空格'
                    ];
                }
                
                // 检查行尾空格
                if (preg_match('/\s+$/', $line)) {
                    $issues[] = [
                        'type' => 'trailing_whitespace',
                        'line' => $lineNumber + 1,
                        'message' => '行尾有多余空格'
                    ];
                }
            }
            
            if (!empty($issues)) {
                $this->analysisResult['code_quality'][] = [
                    'file' => $file['relative_path'],
                    'issues' => $issues
                ];
            }
        }
    }
    
    /**
     * 生成分析摘要
     * 
     * @return void
     */
    private function generateSummary()
    {
        $this->analysisResult['summary'] = [
            'unused_files_count' => count($this->analysisResult['unused_files']),
            'unused_functions_count' => count($this->analysisResult['unused_functions']),
            'unused_variables_count' => count($this->analysisResult['unused_variables']),
            'duplicate_code_blocks' => count($this->analysisResult['duplicate_code']),
            'performance_issues_count' => count($this->analysisResult['performance_issues']),
            'security_issues_count' => count($this->analysisResult['security_issues']),
            'code_quality_files_with_issues' => count($this->analysisResult['code_quality']),
            'analysis_date' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 生成优化建议
     * 
     * @return array 优化建议
     */
    public function generateOptimizationSuggestions()
    {
        $suggestions = [];
        
        if (count($this->analysisResult['unused_files']) > 0) {
            $suggestions[] = [
                'category' => '文件清理',
                'priority' => 'medium',
                'description' => '删除' . count($this->analysisResult['unused_files']) . '个未使用的文件，可以减少项目大小'
            ];
        }
        
        if (count($this->analysisResult['duplicate_code']) > 0) {
            $suggestions[] = [
                'category' => '代码重构',
                'priority' => 'high',
                'description' => '提取' . count($this->analysisResult['duplicate_code']) . '处重复代码为公共函数，提高代码复用性'
            ];
        }
        
        if (count($this->analysisResult['performance_issues']) > 0) {
            $suggestions[] = [
                'category' => '性能优化',
                'priority' => 'high',
                'description' => '修复' . count($this->analysisResult['performance_issues']) . '个性能问题，提升系统响应速度'
            ];
        }
        
        if (count($this->analysisResult['security_issues']) > 0) {
            $suggestions[] = [
                'category' => '安全加固',
                'priority' => 'critical',
                'description' => '修复' . count($this->analysisResult['security_issues']) . '个安全漏洞，提升系统安全性'
            ];
        }
        
        return $suggestions;
    }
}
