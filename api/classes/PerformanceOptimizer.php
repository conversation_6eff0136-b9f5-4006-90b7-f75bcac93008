<?php
/**
 * 性能优化工具类
 * 提供缓存、数据库优化、资源压缩等性能优化功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

class PerformanceOptimizer
{
    /**
     * 缓存目录
     * @var string
     */
    private $cacheDir;
    
    /**
     * 缓存配置
     * @var array
     */
    private $cacheConfig = [
        'default_ttl' => 3600, // 1小时
        'max_cache_size' => 104857600, // 100MB
        'cleanup_probability' => 0.01 // 1%概率清理过期缓存
    ];
    
    /**
     * 构造函数
     * 
     * @param string $cacheDir 缓存目录
     */
    public function __construct($cacheDir = null)
    {
        $this->cacheDir = $cacheDir ?: __DIR__ . '/../../cache/';
        
        // 确保缓存目录存在
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * 设置缓存
     * 
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 过期时间（秒）
     * @return bool
     */
    public function setCache($key, $value, $ttl = null)
    {
        $ttl = $ttl ?: $this->cacheConfig['default_ttl'];
        $cacheFile = $this->getCacheFilePath($key);
        
        $cacheData = [
            'value' => $value,
            'expires_at' => time() + $ttl,
            'created_at' => time()
        ];
        
        $result = file_put_contents($cacheFile, serialize($cacheData), LOCK_EX);
        
        // 随机清理过期缓存
        if (mt_rand() / mt_getrandmax() < $this->cacheConfig['cleanup_probability']) {
            $this->cleanupExpiredCache();
        }
        
        return $result !== false;
    }
    
    /**
     * 获取缓存
     * 
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getCache($key, $default = null)
    {
        $cacheFile = $this->getCacheFilePath($key);
        
        if (!file_exists($cacheFile)) {
            return $default;
        }
        
        $cacheData = unserialize(file_get_contents($cacheFile));
        
        if (!$cacheData || $cacheData['expires_at'] < time()) {
            unlink($cacheFile);
            return $default;
        }
        
        return $cacheData['value'];
    }
    
    /**
     * 删除缓存
     * 
     * @param string $key 缓存键
     * @return bool
     */
    public function deleteCache($key)
    {
        $cacheFile = $this->getCacheFilePath($key);
        
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }
        
        return true;
    }
    
    /**
     * 清空所有缓存
     * 
     * @return int 删除的文件数量
     */
    public function clearAllCache()
    {
        $files = glob($this->cacheDir . '*.cache');
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * 缓存函数执行结果
     * 
     * @param string $key 缓存键
     * @param callable $callback 回调函数
     * @param int $ttl 过期时间
     * @return mixed
     */
    public function remember($key, callable $callback, $ttl = null)
    {
        $cached = $this->getCache($key);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = call_user_func($callback);
        $this->setCache($key, $result, $ttl);
        
        return $result;
    }
    
    /**
     * 获取缓存文件路径
     * 
     * @param string $key 缓存键
     * @return string
     */
    private function getCacheFilePath($key)
    {
        return $this->cacheDir . md5($key) . '.cache';
    }
    
    /**
     * 清理过期缓存
     * 
     * @return int 清理的文件数量
     */
    private function cleanupExpiredCache()
    {
        $files = glob($this->cacheDir . '*.cache');
        $deletedCount = 0;
        $currentTime = time();
        
        foreach ($files as $file) {
            $cacheData = unserialize(file_get_contents($file));
            
            if (!$cacheData || $cacheData['expires_at'] < $currentTime) {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * 压缩CSS文件
     * 
     * @param string $css CSS内容
     * @return string 压缩后的CSS
     */
    public function compressCSS($css)
    {
        // 移除注释
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // 移除多余的空白字符
        $css = preg_replace('/\s+/', ' ', $css);
        
        // 移除不必要的分号
        $css = preg_replace('/;}/', '}', $css);
        
        // 移除空格
        $css = str_replace([' {', '{ ', ' }', '} ', ': ', ' :', '; ', ' ;'], ['{', '{', '}', '}', ':', ':', ';', ';'], $css);
        
        return trim($css);
    }
    
    /**
     * 压缩JavaScript文件
     * 
     * @param string $js JavaScript内容
     * @return string 压缩后的JavaScript
     */
    public function compressJS($js)
    {
        // 移除单行注释
        $js = preg_replace('/\/\/.*$/m', '', $js);
        
        // 移除多行注释
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // 移除多余的空白字符
        $js = preg_replace('/\s+/', ' ', $js);
        
        // 移除不必要的空格
        $js = str_replace([' {', '{ ', ' }', '} ', ' (', '( ', ' )', ') ', ' ;', '; ', ' ,', ', '], ['{', '{', '}', '}', '(', '(', ')', ')', ';', ';', ',', ','], $js);
        
        return trim($js);
    }
    
    /**
     * 优化数据库查询
     * 
     * @param string $sql SQL查询
     * @return array 优化建议
     */
    public function optimizeQuery($sql)
    {
        $suggestions = [];
        
        // 检查是否使用了SELECT *
        if (preg_match('/SELECT\s+\*/i', $sql)) {
            $suggestions[] = [
                'type' => 'select_all',
                'message' => '避免使用 SELECT *，明确指定需要的字段',
                'priority' => 'medium'
            ];
        }
        
        // 检查是否缺少WHERE条件
        if (preg_match('/SELECT.*FROM.*(?!WHERE)/i', $sql) && !preg_match('/WHERE/i', $sql)) {
            $suggestions[] = [
                'type' => 'missing_where',
                'message' => '查询缺少WHERE条件，可能导致全表扫描',
                'priority' => 'high'
            ];
        }
        
        // 检查是否使用了LIMIT
        if (preg_match('/SELECT.*FROM/i', $sql) && !preg_match('/LIMIT/i', $sql)) {
            $suggestions[] = [
                'type' => 'missing_limit',
                'message' => '考虑添加LIMIT限制返回结果数量',
                'priority' => 'low'
            ];
        }
        
        // 检查是否使用了ORDER BY但没有索引
        if (preg_match('/ORDER\s+BY\s+(\w+)/i', $sql, $matches)) {
            $suggestions[] = [
                'type' => 'order_by_index',
                'message' => "确保字段 {$matches[1]} 有索引以优化排序性能",
                'priority' => 'medium'
            ];
        }
        
        // 检查是否使用了子查询
        if (preg_match('/SELECT.*\(.*SELECT.*\)/i', $sql)) {
            $suggestions[] = [
                'type' => 'subquery',
                'message' => '考虑使用JOIN替代子查询以提高性能',
                'priority' => 'medium'
            ];
        }
        
        return $suggestions;
    }
    
    /**
     * 获取系统性能指标
     * 
     * @return array 性能指标
     */
    public function getPerformanceMetrics()
    {
        $metrics = [];
        
        // 内存使用情况
        $metrics['memory'] = [
            'current_usage' => memory_get_usage(true),
            'peak_usage' => memory_get_peak_usage(true),
            'limit' => $this->parseMemoryLimit(ini_get('memory_limit')),
            'usage_percentage' => round((memory_get_usage(true) / $this->parseMemoryLimit(ini_get('memory_limit'))) * 100, 2)
        ];
        
        // 执行时间
        if (isset($_SERVER['REQUEST_TIME_FLOAT'])) {
            $metrics['execution_time'] = microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'];
        }
        
        // 文件包含数量
        $metrics['included_files'] = count(get_included_files());
        
        // 缓存统计
        $cacheFiles = glob($this->cacheDir . '*.cache');
        $cacheSize = 0;
        foreach ($cacheFiles as $file) {
            $cacheSize += filesize($file);
        }
        
        $metrics['cache'] = [
            'files_count' => count($cacheFiles),
            'total_size' => $cacheSize,
            'directory' => $this->cacheDir
        ];
        
        return $metrics;
    }
    
    /**
     * 生成性能报告
     * 
     * @return array 性能报告
     */
    public function generatePerformanceReport()
    {
        $metrics = $this->getPerformanceMetrics();
        $suggestions = [];
        
        // 内存使用建议
        if ($metrics['memory']['usage_percentage'] > 80) {
            $suggestions[] = [
                'category' => 'memory',
                'priority' => 'high',
                'message' => '内存使用率过高，考虑优化代码或增加内存限制'
            ];
        }
        
        // 执行时间建议
        if (isset($metrics['execution_time']) && $metrics['execution_time'] > 2) {
            $suggestions[] = [
                'category' => 'execution_time',
                'priority' => 'medium',
                'message' => '页面执行时间过长，考虑优化数据库查询或添加缓存'
            ];
        }
        
        // 文件包含建议
        if ($metrics['included_files'] > 50) {
            $suggestions[] = [
                'category' => 'file_includes',
                'priority' => 'low',
                'message' => '包含文件数量较多，考虑使用自动加载或合并文件'
            ];
        }
        
        // 缓存大小建议
        if ($metrics['cache']['total_size'] > $this->cacheConfig['max_cache_size']) {
            $suggestions[] = [
                'category' => 'cache_size',
                'priority' => 'medium',
                'message' => '缓存大小超过限制，建议清理过期缓存'
            ];
        }
        
        return [
            'metrics' => $metrics,
            'suggestions' => $suggestions,
            'report_time' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 解析内存限制
     * 
     * @param string $memoryLimit 内存限制字符串
     * @return int 字节数
     */
    private function parseMemoryLimit($memoryLimit)
    {
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int)$memoryLimit;
        
        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
    
    /**
     * 启用输出压缩
     * 
     * @return bool
     */
    public function enableOutputCompression()
    {
        if (!headers_sent() && extension_loaded('zlib')) {
            return ob_start('ob_gzhandler');
        }
        
        return false;
    }
    
    /**
     * 设置缓存头
     * 
     * @param int $maxAge 最大缓存时间（秒）
     * @return void
     */
    public function setCacheHeaders($maxAge = 3600)
    {
        if (!headers_sent()) {
            header('Cache-Control: public, max-age=' . $maxAge);
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT');
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime(__FILE__)) . ' GMT');
        }
    }
}
