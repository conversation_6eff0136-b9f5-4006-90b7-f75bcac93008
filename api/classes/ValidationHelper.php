<?php
/**
 * 数据验证助手类
 * 统一输入验证和数据清理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

class ValidationHelper
{
    /**
     * 验证规则错误消息
     */
    private static $errorMessages = [
        'required' => '字段不能为空',
        'email' => '邮箱格式不正确',
        'min_length' => '长度不能少于 :min 个字符',
        'max_length' => '长度不能超过 :max 个字符',
        'numeric' => '必须是数字',
        'integer' => '必须是整数',
        'phone' => '手机号格式不正确',
        'url' => 'URL格式不正确',
        'date' => '日期格式不正确',
        'in' => '值必须在指定范围内',
        'regex' => '格式不正确'
    ];
    
    /**
     * 验证数据
     * 
     * @param array $data 待验证数据
     * @param array $rules 验证规则
     * @param array $messages 自定义错误消息
     * @return array 验证结果
     */
    public static function validate($data, $rules, $messages = [])
    {
        $errors = [];
        $validatedData = [];
        
        foreach ($rules as $field => $rule) {
            $value = isset($data[$field]) ? $data[$field] : null;
            $fieldRules = is_string($rule) ? explode('|', $rule) : $rule;
            
            foreach ($fieldRules as $singleRule) {
                $ruleName = $singleRule;
                $ruleValue = null;
                
                // 解析带参数的规则
                if (strpos($singleRule, ':') !== false) {
                    list($ruleName, $ruleValue) = explode(':', $singleRule, 2);
                }
                
                $error = self::validateField($field, $value, $ruleName, $ruleValue, $messages);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
            
            // 如果没有错误，添加到验证通过的数据中
            if (!isset($errors[$field])) {
                $validatedData[$field] = self::sanitizeValue($value, $fieldRules);
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'data' => $validatedData
        ];
    }
    
    /**
     * 验证单个字段
     * 
     * @param string $field 字段名
     * @param mixed $value 字段值
     * @param string $rule 验证规则
     * @param mixed $ruleValue 规则参数
     * @param array $messages 自定义错误消息
     * @return string|null 错误消息
     */
    private static function validateField($field, $value, $rule, $ruleValue, $messages)
    {
        switch ($rule) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'min_length':
                if (!empty($value) && mb_strlen($value) < (int)$ruleValue) {
                    return self::getErrorMessage($field, $rule, $messages, ['min' => $ruleValue]);
                }
                break;
                
            case 'max_length':
                if (!empty($value) && mb_strlen($value) > (int)$ruleValue) {
                    return self::getErrorMessage($field, $rule, $messages, ['max' => $ruleValue]);
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'integer':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'phone':
                if (!empty($value) && !preg_match('/^1[3-9]\d{9}$/', $value)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'date':
                if (!empty($value) && !strtotime($value)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'in':
                $allowedValues = explode(',', $ruleValue);
                if (!empty($value) && !in_array($value, $allowedValues)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
                
            case 'regex':
                if (!empty($value) && !preg_match($ruleValue, $value)) {
                    return self::getErrorMessage($field, $rule, $messages);
                }
                break;
        }
        
        return null;
    }
    
    /**
     * 获取错误消息
     * 
     * @param string $field 字段名
     * @param string $rule 验证规则
     * @param array $messages 自定义错误消息
     * @param array $replacements 替换参数
     * @return string 错误消息
     */
    private static function getErrorMessage($field, $rule, $messages, $replacements = [])
    {
        $key = "{$field}.{$rule}";
        
        // 优先使用自定义消息
        if (isset($messages[$key])) {
            $message = $messages[$key];
        } elseif (isset($messages[$rule])) {
            $message = $messages[$rule];
        } else {
            $message = self::$errorMessages[$rule] ?? '验证失败';
        }
        
        // 替换参数
        foreach ($replacements as $key => $value) {
            $message = str_replace(":{$key}", $value, $message);
        }
        
        return $message;
    }
    
    /**
     * 清理数据
     * 
     * @param mixed $value 原始值
     * @param array $rules 验证规则
     * @return mixed 清理后的值
     */
    private static function sanitizeValue($value, $rules)
    {
        if (is_string($value)) {
            $value = trim($value);
            
            // 根据规则进行特殊处理
            if (in_array('email', $rules)) {
                $value = filter_var($value, FILTER_SANITIZE_EMAIL);
            } elseif (in_array('url', $rules)) {
                $value = filter_var($value, FILTER_SANITIZE_URL);
            } elseif (in_array('integer', $rules)) {
                $value = (int)$value;
            } elseif (in_array('numeric', $rules)) {
                $value = is_numeric($value) ? (float)$value : $value;
            }
        }
        
        return $value;
    }
    
    /**
     * 快速验证必填字段
     * 
     * @param array $data 数据
     * @param array $requiredFields 必填字段
     * @return array 验证结果
     */
    public static function validateRequired($data, $requiredFields)
    {
        $rules = [];
        foreach ($requiredFields as $field) {
            $rules[$field] = 'required';
        }
        
        return self::validate($data, $rules);
    }
    
    /**
     * 验证并抛出异常
     * 
     * @param array $data 待验证数据
     * @param array $rules 验证规则
     * @param array $messages 自定义错误消息
     * @return array 验证通过的数据
     * @throws Exception 验证失败时抛出异常
     */
    public static function validateOrFail($data, $rules, $messages = [])
    {
        $result = self::validate($data, $rules, $messages);
        
        if (!$result['valid']) {
            $firstError = reset($result['errors']);
            $errorMessage = is_array($firstError) ? reset($firstError) : $firstError;
            throw new Exception($errorMessage);
        }
        
        return $result['data'];
    }
}
